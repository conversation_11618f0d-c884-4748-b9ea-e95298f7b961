[{"id": 1, "sample_id": 1, "from_tenant_id": 1, "to_tenant_id": 2, "reason": "Specialized testing required", "notes": "Sample requires advanced lipid analysis", "status": "Completed", "created_at": "2024-01-16T10:00:00", "updated_at": "2024-01-16T14:00:00", "created_by": 2, "transferred_at": "2024-01-16T12:00:00", "received_at": "2024-01-16T14:00:00", "received_by": 15}, {"id": 2, "sample_id": 3, "from_tenant_id": 3, "to_tenant_id": 1, "reason": "Hub processing required", "notes": "Complex urine analysis needed", "status": "Transferred", "created_at": "2024-01-17T11:00:00", "updated_at": "2024-01-17T13:00:00", "created_by": 17, "transferred_at": "2024-01-17T13:00:00", "received_at": null, "received_by": null}, {"id": 3, "sample_id": 5, "from_tenant_id": 2, "to_tenant_id": 1, "reason": "Quality control review", "notes": "Thyroid results need verification", "status": "Pending", "created_at": "2024-01-19T16:00:00", "updated_at": "2024-01-19T16:00:00", "created_by": 15, "transferred_at": null, "received_at": null, "received_by": null}, {"id": 4, "sample_id": "103", "from_tenant_id": 1, "to_tenant_id": "2", "reason": "Specialized testing required", "notes": "test", "status": "Pending", "created_at": "2025-06-11T11:33:11.982180", "updated_at": "2025-06-11T11:33:11.982195", "created_by": 1, "transferred_at": null, "received_at": null, "received_by": null}, {"id": 5, "sample_id": "103", "from_tenant_id": 1, "to_tenant_id": "2", "reason": "Specialized testing required", "notes": "test", "status": "Pending", "created_at": "2025-06-11T11:34:31.554291", "updated_at": "2025-06-11T11:34:31.554299", "created_by": 1, "transferred_at": null, "received_at": null, "received_by": null}, {"id": 6, "sample_id": "40", "from_tenant_id": 1, "to_tenant_id": "2", "reason": "Specialized testing required", "notes": "check", "status": "Pending", "created_at": "2025-06-11T11:39:24.835066", "updated_at": "2025-06-11T11:39:24.835074", "created_by": 1, "transferred_at": null, "received_at": null, "received_by": null}, {"id": 7, "sample_id": "103", "from_tenant_id": 1, "to_tenant_id": "2", "reason": "Specialized testing required", "notes": "check", "status": "Pending", "created_at": "2025-06-11T12:02:57.434595", "updated_at": "2025-06-11T12:02:57.434603", "created_by": 1, "transferred_at": null, "received_at": null, "received_by": null}, {"id": 8, "sample_id": "61", "from_tenant_id": 1, "to_tenant_id": "2", "reason": "Specialized testing required", "notes": "chekcing", "status": "Pending", "created_at": "2025-06-11T12:56:27.260774", "updated_at": "2025-06-11T12:56:27.260781", "created_by": 1, "transferred_at": null, "received_at": null, "received_by": null}, {"id": 9, "sample_id": "102", "from_tenant_id": 1, "to_tenant_id": "2", "reason": "Specialized testing required", "notes": "check", "status": "Pending", "created_at": "2025-06-11T13:10:32.083428", "updated_at": "2025-06-11T13:10:32.083436", "created_by": 1, "transferred_at": null, "received_at": null, "received_by": null}, {"id": 10, "sample_id": "78", "from_tenant_id": 1, "to_tenant_id": "2", "reason": "Specialized testing required", "notes": "test", "status": "Pending", "created_at": "2025-06-11T14:41:45.646678", "updated_at": "2025-06-11T14:41:45.646688", "created_by": 1, "transferred_at": null, "received_at": null, "received_by": null}, {"id": 11, "sample_id": "5", "from_tenant_id": 1, "to_tenant_id": "2", "reason": "Specialized testing required", "notes": "", "status": "Pending", "created_at": "2025-06-14T20:04:33.481099", "updated_at": "2025-06-14T20:04:33.481099", "created_by": 1, "transferred_at": null, "received_at": null, "received_by": null}]