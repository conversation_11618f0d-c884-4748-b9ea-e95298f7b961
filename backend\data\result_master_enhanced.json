[{"id": 1, "test_name": "Abs.Basophils in #.", "test_code": "001687", "department": "HAEMATOLOGY", "result_name": "Abs.Basophils in #.", "parameter_name": "Abs.Basophils in #.", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.613571", "updated_at": "2025-07-08T19:27:20.613578"}, {"id": 2, "test_name": "Abs.Eosinophils in #.", "test_code": "001685", "department": "HAEMATOLOGY", "result_name": "Abs.Eosinophils in #.", "parameter_name": "Abs.Eosinophils in #.", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.613743", "updated_at": "2025-07-08T19:27:20.613747"}, {"id": 3, "test_name": "Abs.Lymphocyte in #.", "test_code": "001684", "department": "HAEMATOLOGY", "result_name": "Abs.Lymphocyte in #.", "parameter_name": "Abs.Lymphocyte in #.", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.613866", "updated_at": "2025-07-08T19:27:20.613869"}, {"id": 4, "test_name": "Abs.Monocyte in #.", "test_code": "001686", "department": "HAEMATOLOGY", "result_name": "Abs.Monocyte in #.", "parameter_name": "Abs.Monocyte in #.", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.613977", "updated_at": "2025-07-08T19:27:20.613980"}, {"id": 5, "test_name": "Abs.Neutrophils in #.", "test_code": "001683", "department": "HAEMATOLOGY", "result_name": "Abs.Neutrophils in #.", "parameter_name": "Abs.Neutrophils in #.", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.614087", "updated_at": "2025-07-08T19:27:20.614090"}, {"id": 6, "test_name": "ABSOLUTE EOSINOPHIL COUNT", "test_code": "000330", "department": "HAEMATOLOGY", "result_name": "ABSOLUTE EOSINOPHIL COUNT", "parameter_name": "ABSOLUTE EOSINOPHIL COUNT", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Automated", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.614199", "updated_at": "2025-07-08T19:27:20.614202"}, {"id": 7, "test_name": "ABSOLUTE LYMPHOCYTE COUNT", "test_code": "000331", "department": "HAEMATOLOGY", "result_name": "ABSOLUTE LYMPHOCYTE COUNT", "parameter_name": "ABSOLUTE LYMPHOCYTE COUNT", "unit": "cells/cumm", "result_type": "-", "reference_range": "1000 - 3000", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Automated", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.614317", "updated_at": "2025-07-08T19:27:20.614320"}, {"id": 8, "test_name": "ABSOLUTE NEUTROPHIL COUNT", "test_code": "000332", "department": "HAEMATOLOGY", "result_name": "ABSOLUTE NEUTROPHIL COUNT", "parameter_name": "ABSOLUTE NEUTROPHIL COUNT", "unit": "cells/cumm", "result_type": "-", "reference_range": "2000 - 7000", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Automated                        ", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.614429", "updated_at": "2025-07-08T19:27:20.614432"}, {"id": 9, "test_name": "Anti Platelet Antibody", "test_code": "000333", "department": "HAEMATOLOGY", "result_name": "Anti Platelet Antibody", "parameter_name": "Anti Platelet Antibody", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IF", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.614537", "updated_at": "2025-07-08T19:27:20.614540"}, {"id": 10, "test_name": "BLEEDING TIME", "test_code": "000342", "department": "HAEMATOLOGY", "result_name": "BLEEDING TIME", "parameter_name": "BLEEDING TIME", "unit": "minutes", "result_type": "Pick List", "reference_range": "1 to 6", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IVY", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.614646", "updated_at": "2025-07-08T19:27:20.614649"}, {"id": 11, "test_name": "BLOOD GROUP&RH-GEL METHOD", "test_code": "000428", "department": "HAEMATOLOGY", "result_name": "BLOOD GROUP&RH-GEL METHOD", "parameter_name": "BLOOD GROUP&RH-GEL METHOD", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "GEL METHOD", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.614753", "updated_at": "2025-07-08T19:27:20.614756"}, {"id": 12, "test_name": "Chromosome Analysis - Product of Conception", "test_code": "000922", "department": "HAEMATOLOGY", "result_name": "Chromosome Analysis - Product of Conception", "parameter_name": "Chromosome Analysis - Product of Conception", "unit": null, "result_type": "Template", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.614859", "updated_at": "2025-07-08T19:27:20.614862"}, {"id": 13, "test_name": "CLOTTING TIME", "test_code": "000347", "department": "HAEMATOLOGY", "result_name": "CLOTTING TIME", "parameter_name": "CLOTTING TIME", "unit": "minutes", "result_type": "Pick List", "reference_range": "5 - 10", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "<PERSON> and <PERSON> method", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.614966", "updated_at": "2025-07-08T19:27:20.614968"}, {"id": 14, "test_name": "COLD AGGLUTININ", "test_code": "000348", "department": "HAEMATOLOGY", "result_name": "COLD AGGLUTININ", "parameter_name": "COLD AGGLUTININ", "unit": null, "result_type": "Pick List", "reference_range": "NEGATIVE UPTO 1:32", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.615067", "updated_at": "2025-07-08T19:27:20.615070"}, {"id": 15, "test_name": "Calcium, Urine 24Hr", "test_code": "000040", "department": "HAEMATOLOGY", "result_name": "Calcium, Urine 24Hr", "parameter_name": "Calcium, Urine 24Hr", "unit": null, "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.615168", "updated_at": "2025-07-08T19:27:20.615171"}, {"id": 16, "test_name": "17 HYDROXY CORTICO STEROID 24 HR URINE", "test_code": "000003", "department": "HAEMATOLOGY", "result_name": "17 HYDROXY CORTICO STEROID 24 HR URINE", "parameter_name": "17 HYDROXY CORTICO STEROID 24 HR URINE", "unit": "mg/24hrs", "result_type": null, "reference_range": "< 1 Years     :  <=  1.0 (Both) 1 - 10 Years  :  3 - 6 (Both) > 10 Years    :  3 - 10 (Male)", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Column Chromatography", "specimen_type": "24 H<PERSON> <PERSON><PERSON>", "container": "<PERSON><PERSON><PERSON>er", "instructions": "10 ml of 50% HCL as a preservative, Total volume to be mentioned", "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.615275", "updated_at": "2025-07-08T19:27:20.615278"}, {"id": 17, "test_name": "DCP- Decarboxy Prothrombin PIVKA II", "test_code": "001599", "department": "HAEMATOLOGY", "result_name": "DCP- Decarboxy Prothrombin PIVKA II", "parameter_name": "DCP- Decarboxy Prothrombin PIVKA II", "unit": "mAU/ml", "result_type": "-", "reference_range": "17.36 - 50.90", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "CMIA", "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.615384", "updated_at": "2025-07-08T19:27:20.615387"}, {"id": 18, "test_name": "DIFFERENTIAL COUNT-5 Part", "test_code": "000350", "department": "HAEMATOLOGY", "result_name": "DIFFERENTIAL COUNT-5 Part", "parameter_name": "DIFFERENTIAL COUNT-5 Part", "unit": null, "result_type": "No Unit / Ref. Value", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Automated", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.615493", "updated_at": "2025-07-08T19:27:20.615496"}, {"id": 19, "test_name": "Differential Count", "test_code": "001638", "department": "HAEMATOLOGY", "result_name": "Differential Count", "parameter_name": "Differential Count", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Electrical Impedance", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.615597", "updated_at": "2025-07-08T19:27:20.615599"}, {"id": 20, "test_name": "DIRECT COOMBS TEST", "test_code": "000351", "department": "HAEMATOLOGY", "result_name": "DIRECT COOMBS TEST", "parameter_name": "DIRECT COOMBS TEST", "unit": null, "result_type": "Pick List", "reference_range": "Negative", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Column Agglutination", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.615701", "updated_at": "2025-07-08T19:27:20.615704"}, {"id": 21, "test_name": "ESR", "test_code": "000353", "department": "HAEMATOLOGY", "result_name": "ESR", "parameter_name": "ESR", "unit": null, "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "<PERSON><PERSON><PERSON><PERSON> Method", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.615803", "updated_at": "2025-07-08T19:27:20.615806"}, {"id": 22, "test_name": "Factor X Functional", "test_code": "001348", "department": "HAEMATOLOGY", "result_name": "Factor X Functional", "parameter_name": "Factor X Functional", "unit": "%", "result_type": "Pick List", "reference_range": "70.00 - 120.00", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "COAGULATION", "specimen_type": "Citrate Plasma", "container": "Citrate Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.615907", "updated_at": "2025-07-08T19:27:20.615909"}, {"id": 23, "test_name": "FDP", "test_code": "000076", "department": "HAEMATOLOGY", "result_name": "FDP", "parameter_name": "FDP", "unit": null, "result_type": "Pick List", "reference_range": "Negative", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Agglutination", "specimen_type": "Citrate Plasma", "container": "Citrate Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.616014", "updated_at": "2025-07-08T19:27:20.616017"}, {"id": 24, "test_name": "FIBRINOGEN", "test_code": "000327", "department": "HAEMATOLOGY", "result_name": "FIBRINOGEN", "parameter_name": "FIBRINOGEN", "unit": "mg/dL", "result_type": "Pick List", "reference_range": "200 - 400", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "COAGULATION", "specimen_type": "Citrate Plasma", "container": "Citrate Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.616118", "updated_at": "2025-07-08T19:27:20.616121"}, {"id": 25, "test_name": "FILARIAL ANTIBODY", "test_code": "001276", "department": "HAEMATOLOGY", "result_name": "FILARIAL ANTIBODY", "parameter_name": "FILARIAL ANTIBODY", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Immuno Chromatography", "specimen_type": "SERUM", "container": "Serum Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.616222", "updated_at": "2025-07-08T19:27:20.616225"}, {"id": 26, "test_name": "FILARIAL ANTIGEN", "test_code": "000375", "department": "HAEMATOLOGY", "result_name": "FILARIAL ANTIGEN", "parameter_name": "FILARIAL ANTIGEN", "unit": null, "result_type": "Pick List", "reference_range": "NEGATIVE  The Test is structured to indicate the presence or absence of W.ban<PERSON> antigen in the sample.  The absence of antigen does not exclude Filariasis caused by other nematode species.", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IC", "specimen_type": null, "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.616325", "updated_at": "2025-07-08T19:27:20.616327"}, {"id": 27, "test_name": "GIEMSA STAINING", "test_code": "000423", "department": "HAEMATOLOGY", "result_name": "GIEMSA STAINING", "parameter_name": "GIEMSA STAINING", "unit": null, "result_type": "No Unit / Ref. Value", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.616425", "updated_at": "2025-07-08T19:27:20.616428"}, {"id": 28, "test_name": "Haemoglobin", "test_code": "000361", "department": "HAEMATOLOGY", "result_name": "Haemoglobin", "parameter_name": "Haemoglobin", "unit": "g/dL", "result_type": "-", "reference_range": "12.0 - 16.0\n14.0 - 18.0", "critical_low": 7.0, "critical_high": 19.0, "decimal_places": 1, "method": "Non-Cyanide Haemoglobin Analysis", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.616532", "updated_at": "2025-07-08T19:27:20.616535"}, {"id": 29, "test_name": "Haemoglobin, Urine", "test_code": "000280", "department": "HAEMATOLOGY", "result_name": "Haemoglobin, Urine", "parameter_name": "Haemoglobin, Urine", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.616635", "updated_at": "2025-07-08T19:27:20.616638"}, {"id": 30, "test_name": "HCT", "test_code": "001639", "department": "HAEMATOLOGY", "result_name": "HCT", "parameter_name": "HCT", "unit": "%", "result_type": null, "reference_range": "37.000 - 54.000", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": null, "specimen_type": "BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.616738", "updated_at": "2025-07-08T19:27:20.616741"}, {"id": 31, "test_name": "HCT (P.C.V).", "test_code": "001689", "department": "HAEMATOLOGY", "result_name": "HCT (P.C.V).", "parameter_name": "HCT (P.C.V).", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Electrical Impedance", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.616838", "updated_at": "2025-07-08T19:27:20.616841"}, {"id": 32, "test_name": "Immature Platelet Fraction (IPF)", "test_code": "001537", "department": "HAEMATOLOGY", "result_name": "Immature Platelet Fraction (IPF)", "parameter_name": "Immature Platelet Fraction (IPF)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Fluorescent Flow Cytometry", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.616943", "updated_at": "2025-07-08T19:27:20.616945"}, {"id": 33, "test_name": "INDIRECT COOMBS TEST", "test_code": "000364", "department": "HAEMATOLOGY", "result_name": "INDIRECT COOMBS TEST", "parameter_name": "INDIRECT COOMBS TEST", "unit": null, "result_type": "Pick List\t", "reference_range": "Negative", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Column Agglutination", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.617045", "updated_at": "2025-07-08T19:27:20.617048"}, {"id": 34, "test_name": "Large Immature Cells#.", "test_code": "001682", "department": "HAEMATOLOGY", "result_name": "Large Immature Cells#.", "parameter_name": "Large Immature Cells#.", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.617149", "updated_at": "2025-07-08T19:27:20.617152"}, {"id": 35, "test_name": "Large Immature Cells.", "test_code": "001681", "department": "HAEMATOLOGY", "result_name": "Large Immature Cells.", "parameter_name": "Large Immature Cells.", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "EDTA BLOOD", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.617248", "updated_at": "2025-07-08T19:27:20.617251"}, {"id": 36, "test_name": "Lupus Anticoagulant (dRVVT)", "test_code": "000326", "department": "HAEMATOLOGY", "result_name": "Lupus Anticoagulant (dRVVT)", "parameter_name": "Lupus Anticoagulant (dRVVT)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "COAGULATION", "specimen_type": "Citrate Plasma", "container": "Citrate Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.617349", "updated_at": "2025-07-08T19:27:20.617352"}, {"id": 37, "test_name": "MALARIAL  PARASITE (CARD)", "test_code": "000424", "department": "HAEMATOLOGY", "result_name": "MALARIAL  PARASITE (CARD)", "parameter_name": "MALARIAL  PARASITE (CARD)", "unit": null, "result_type": "Pick List", "reference_range": "NEGATIVE", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ICT", "specimen_type": "EDTA BLOOD", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.617450", "updated_at": "2025-07-08T19:27:20.617452"}, {"id": 38, "test_name": "MALARIAL  PARASITE (SLIDE)", "test_code": "000366", "department": "HAEMATOLOGY", "result_name": "MALARIAL  PARASITE (SLIDE)", "parameter_name": "MALARIAL  PARASITE (SLIDE)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "EDTA BLOOD", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.617548", "updated_at": "2025-07-08T19:27:20.617551"}, {"id": 39, "test_name": "MALARIAL ANTIGEN (Vivax & Falciparum)", "test_code": "000430", "department": "HAEMATOLOGY", "result_name": "MALARIAL ANTIGEN (Vivax & Falciparum)", "parameter_name": "MALARIAL ANTIGEN (Vivax & Falciparum)", "unit": null, "result_type": "Pick List", "reference_range": "NEGATIVE", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IC", "specimen_type": "EDTA BLOOD", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.617668", "updated_at": "2025-07-08T19:27:20.617671"}, {"id": 40, "test_name": "Malarial Parasite (QBC)", "test_code": "000427", "department": "HAEMATOLOGY", "result_name": "Malarial Parasite (QBC)", "parameter_name": "Malarial Parasite (QBC)", "unit": "per QBC Field", "result_type": "Pick List", "reference_range": "< 1    Parasite   : + 1-10   Parasites  : ++ 11-100 Parasites  : +++ > 100  Parasites  : +++", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "QBC", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.617774", "updated_at": "2025-07-08T19:27:20.617777"}, {"id": 41, "test_name": "MCH", "test_code": "000369", "department": "HAEMATOLOGY", "result_name": "MCH", "parameter_name": "MCH", "unit": "pg", "result_type": "Numeric", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Calculated", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.617878", "updated_at": "2025-07-08T19:27:20.617880"}, {"id": 42, "test_name": "MCHC", "test_code": "000370", "department": "HAEMATOLOGY", "result_name": "MCHC", "parameter_name": "MCHC", "unit": "g/L", "result_type": "Numeric", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Calculated", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.617980", "updated_at": "2025-07-08T19:27:20.617983"}, {"id": 43, "test_name": "MCV", "test_code": "000371", "department": "HAEMATOLOGY", "result_name": "MCV", "parameter_name": "MCV", "unit": "fl", "result_type": "Numeric", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Calculated", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.618082", "updated_at": "2025-07-08T19:27:20.618085"}, {"id": 44, "test_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Blood", "test_code": "000372", "department": "HAEMATOLOGY", "result_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Blood", "parameter_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Blood", "unit": "%", "result_type": "Numeric", "reference_range": "Less than 1.5", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Spectrophotometry", "specimen_type": "HEPARIN BLOOD", "container": "<PERSON><PERSON><PERSON>", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.618185", "updated_at": "2025-07-08T19:27:20.618190"}, {"id": 45, "test_name": "MICROFILARIA (MF)", "test_code": "000374", "department": "HAEMATOLOGY", "result_name": "MICROFILARIA (MF)", "parameter_name": "MICROFILARIA (MF)", "unit": null, "result_type": "Pick List", "reference_range": "Negative", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Microscopic", "specimen_type": "WHOLE BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.618293", "updated_at": "2025-07-08T19:27:20.618295"}, {"id": 46, "test_name": "MICROFILARIA (MF)  by QBC", "test_code": "000431", "department": "HAEMATOLOGY", "result_name": "MICROFILARIA (MF)  by QBC", "parameter_name": "MICROFILARIA (MF)  by QBC", "unit": null, "result_type": "Pick List", "reference_range": "( QBC )", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "QBC", "specimen_type": "EDTA BLOOD", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.618393", "updated_at": "2025-07-08T19:27:20.618396"}, {"id": 47, "test_name": "MPV", "test_code": "001640", "department": "HAEMATOLOGY", "result_name": "MPV", "parameter_name": "MPV", "unit": "fl", "result_type": "Numeric", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.618492", "updated_at": "2025-07-08T19:27:20.618494"}, {"id": 48, "test_name": "MYOGLOBIN-SERUM", "test_code": "000426", "department": "HAEMATOLOGY", "result_name": "MYOGLOBIN-SERUM", "parameter_name": "MYOGLOBIN-SERUM", "unit": "ug/L", "result_type": "Numeric", "reference_range": "25-58", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "ECLIA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.618593", "updated_at": "2025-07-08T19:27:20.618595"}, {"id": 49, "test_name": "NASAL SMEAR FOR EOSINOPHILS", "test_code": "000425", "department": "HAEMATOLOGY", "result_name": "NASAL SMEAR FOR EOSINOPHILS", "parameter_name": "NASAL SMEAR FOR EOSINOPHILS", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "NASAL SMEAR", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.618690", "updated_at": "2025-07-08T19:27:20.618693"}, {"id": 50, "test_name": "P-LCR", "test_code": "001643", "department": "HAEMATOLOGY", "result_name": "P-LCR", "parameter_name": "P-LCR", "unit": "%", "result_type": "Numeric", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.618792", "updated_at": "2025-07-08T19:27:20.618795"}, {"id": 51, "test_name": "PARTIAL THROMBOPLASTIN TIME (APTT)", "test_code": "000328", "department": "HAEMATOLOGY", "result_name": "PARTIAL THROMBOPLASTIN TIME (APTT)", "parameter_name": "PARTIAL THROMBOPLASTIN TIME (APTT)", "unit": null, "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "COAGULATION", "specimen_type": "Citrate Plasma", "container": "Citrate Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.619085", "updated_at": "2025-07-08T19:27:20.619210"}, {"id": 52, "test_name": "PCT", "test_code": "001642", "department": "HAEMATOLOGY", "result_name": "PCT", "parameter_name": "PCT", "unit": "mL/L", "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.619637", "updated_at": "2025-07-08T19:27:20.619643"}, {"id": 53, "test_name": "PCV", "test_code": "000385", "department": "HAEMATOLOGY", "result_name": "PCV", "parameter_name": "PCV", "unit": "%", "result_type": "-", "reference_range": "Male  : 40 - 54 Female: 37 - 47", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Electrical Impedance", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.620035", "updated_at": "2025-07-08T19:27:20.620041"}, {"id": 54, "test_name": "PDW", "test_code": "001641", "department": "HAEMATOLOGY", "result_name": "PDW", "parameter_name": "PDW", "unit": "%", "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.620249", "updated_at": "2025-07-08T19:27:20.620253"}, {"id": 55, "test_name": "Platelet count", "test_code": "000391", "department": "HAEMATOLOGY", "result_name": "Platelet count", "parameter_name": "Platelet count", "unit": "cells/cumm", "result_type": null, "reference_range": null, "critical_low": 80000.0, "critical_high": 700000.0, "decimal_places": 0, "method": "Electrical Impedance", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.620417", "updated_at": "2025-07-08T19:27:20.620421"}, {"id": 56, "test_name": "PDW-CV.", "test_code": "001697", "department": "HAEMATOLOGY", "result_name": "PDW-CV.", "parameter_name": "PDW-CV.", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.620650", "updated_at": "2025-07-08T19:27:20.620655"}, {"id": 57, "test_name": "PDW-SD.", "test_code": "001696", "department": "HAEMATOLOGY", "result_name": "PDW-SD.", "parameter_name": "PDW-SD.", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.620866", "updated_at": "2025-07-08T19:27:20.620868"}, {"id": 58, "test_name": "PERIPHERAL BLOOD SMEAR", "test_code": "000442", "department": "HAEMATOLOGY", "result_name": "PERIPHERAL BLOOD SMEAR", "parameter_name": "PERIPHERAL BLOOD SMEAR", "unit": null, "result_type": "No Unit / Ref. Value", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.621004", "updated_at": "2025-07-08T19:27:20.621007"}, {"id": 59, "test_name": "Peripheral Smear", "test_code": "001298", "department": "HAEMATOLOGY", "result_name": "Peripheral Smear", "parameter_name": "Peripheral Smear", "unit": null, "result_type": "Template", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.621130", "updated_at": "2025-07-08T19:27:20.621133"}, {"id": 60, "test_name": "PH ( BLOOD)", "test_code": "000387", "department": "HAEMATOLOGY", "result_name": "PH ( BLOOD)", "parameter_name": "PH ( BLOOD)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "EDTA BLOOD", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.621236", "updated_at": "2025-07-08T19:27:20.621239"}, {"id": 61, "test_name": "PLATELET COUNT-5P", "test_code": "001701", "department": "HAEMATOLOGY", "result_name": "PLATELET COUNT-5P", "parameter_name": "PLATELET COUNT-5P", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.621337", "updated_at": "2025-07-08T19:27:20.621340"}, {"id": 62, "test_name": "PLATELET COUNT.", "test_code": "001265", "department": "HAEMATOLOGY", "result_name": "PLATELET COUNT.", "parameter_name": "PLATELET COUNT.", "unit": null, "result_type": "-", "reference_range": "150000 - 400000.", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.621442", "updated_at": "2025-07-08T19:27:20.621444"}, {"id": 63, "test_name": "Platelets Count.", "test_code": "001695", "department": "HAEMATOLOGY", "result_name": "Platelets Count.", "parameter_name": "Platelets Count.", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Electrical Impedance", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.621544", "updated_at": "2025-07-08T19:27:20.621547"}, {"id": 64, "test_name": "PORPHOBILINOGEN -QUANTITAITVE", "test_code": "000390", "department": "HAEMATOLOGY", "result_name": "PORPHOBILINOGEN -QUANTITAITVE", "parameter_name": "PORPHOBILINOGEN -QUANTITAITVE", "unit": "mg/dL", "result_type": "-", "reference_range": "Spot sample  :  0 to 0.2 mg/dl    24 hrs sample :  0 - 3.4 mg/24hrs", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Column Chromatography", "specimen_type": "SPOT URINE", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.621681", "updated_at": "2025-07-08T19:27:20.621684"}, {"id": 65, "test_name": "PROTHROMBIN TIME", "test_code": "000329", "department": "HAEMATOLOGY", "result_name": "PROTHROMBIN TIME", "parameter_name": "PROTHROMBIN TIME", "unit": null, "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "Citrate Plasma", "container": "Citrate Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.621799", "updated_at": "2025-07-08T19:27:20.621801"}, {"id": 66, "test_name": "RDW - CV", "test_code": "000432", "department": "HAEMATOLOGY", "result_name": "RDW - CV", "parameter_name": "RDW - CV", "unit": "%", "result_type": "-", "reference_range": "2025-10-15T00:00:00", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Automated", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.621906", "updated_at": "2025-07-08T19:27:20.621908"}, {"id": 67, "test_name": "RDW - SD", "test_code": "001637", "department": "HAEMATOLOGY", "result_name": "RDW - SD", "parameter_name": "RDW - SD", "unit": "fl", "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "Automated", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.622008", "updated_at": "2025-07-08T19:27:20.622010"}, {"id": 68, "test_name": "RDW-CV.", "test_code": "001693", "department": "HAEMATOLOGY", "result_name": "RDW-CV.", "parameter_name": "RDW-CV.", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Automated", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.622107", "updated_at": "2025-07-08T19:27:20.622109"}, {"id": 69, "test_name": "RDW-SD.", "test_code": "001694", "department": "HAEMATOLOGY", "result_name": "RDW-SD.", "parameter_name": "RDW-SD.", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Automated", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.622338", "updated_at": "2025-07-08T19:27:20.622341"}, {"id": 70, "test_name": "Red Blood Cell (RBC) Count", "test_code": "000396", "department": "HAEMATOLOGY", "result_name": "Red Blood Cell (RBC) Count", "parameter_name": "Red Blood Cell (RBC) Count", "unit": "million/cumm", "result_type": "Pick List", "reference_range": "Female : 4.00 - 5.50\nMale : 4.50 - 6.20", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "Electrical Impedance", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.622444", "updated_at": "2025-07-08T19:27:20.622446"}, {"id": 71, "test_name": "RED BLOOD CELL COUNT-5P", "test_code": "001700", "department": "HAEMATOLOGY", "result_name": "RED BLOOD CELL COUNT-5P", "parameter_name": "RED BLOOD CELL COUNT-5P", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.622542", "updated_at": "2025-07-08T19:27:20.622544"}, {"id": 72, "test_name": "RETICULOCYTE COUNT", "test_code": "000398", "department": "HAEMATOLOGY", "result_name": "RETICULOCYTE COUNT", "parameter_name": "RETICULOCYTE COUNT", "unit": "%", "result_type": "-", "reference_range": "At Birth        : 1.1 - 4.5  Neonates        : 0.1 - 1.5  Infants         : 2.0 - 6.0  child(>6 months): 0.5 - 4.0  Adult           : 0.5 - 2.5", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Microscopic", "specimen_type": "EDTA BLOOD", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.622643", "updated_at": "2025-07-08T19:27:20.622645"}, {"id": 73, "test_name": "RH ANTIBODY TITRE", "test_code": "000400", "department": "HAEMATOLOGY", "result_name": "RH ANTIBODY TITRE", "parameter_name": "RH ANTIBODY TITRE", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.622740", "updated_at": "2025-07-08T19:27:20.622743"}, {"id": 74, "test_name": "SPECIFIC GRAVITY URINE", "test_code": "000405", "department": "HAEMATOLOGY", "result_name": "SPECIFIC GRAVITY URINE", "parameter_name": "SPECIFIC GRAVITY URINE", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 3, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.622871", "updated_at": "2025-07-08T19:27:20.622874"}, {"id": 75, "test_name": "RVVT - Russell viper venom time", "test_code": "000429", "department": "HAEMATOLOGY", "result_name": "RVVT - Russell viper venom time", "parameter_name": "RVVT - Russell viper venom time", "unit": null, "result_type": "Pick List\t", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ACM", "specimen_type": "Citrate Plasma", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.622970", "updated_at": "2025-07-08T19:27:20.622973"}, {"id": 76, "test_name": "SICKLING TEST", "test_code": "000402", "department": "HAEMATOLOGY", "result_name": "SICKLING TEST", "parameter_name": "SICKLING TEST", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.623067", "updated_at": "2025-07-08T19:27:20.623070"}, {"id": 77, "test_name": "Thrombin Time", "test_code": "000436", "department": "HAEMATOLOGY", "result_name": "Thrombin Time", "parameter_name": "Thrombin Time", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Electromechanical Clot Detection", "specimen_type": "Citrate Plasma", "container": "Citrate Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.623165", "updated_at": "2025-07-08T19:27:20.623168"}, {"id": 78, "test_name": "Total WBC count", "test_code": "000407", "department": "HAEMATOLOGY", "result_name": "Total WBC count", "parameter_name": "Total WBC count", "unit": "cells/cumm", "result_type": "-", "reference_range": "4000.000 - 10000.000", "critical_low": 2000.0, "critical_high": 50000.0, "decimal_places": 0, "method": "Fluorescent Flow Cytometry", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.623269", "updated_at": "2025-07-08T19:27:20.623271"}, {"id": 79, "test_name": "Tuberculin skin (Mantoux) Test", "test_code": "000368", "department": "HAEMATOLOGY", "result_name": "Tuberculin skin (Mantoux) Test", "parameter_name": "Tuberculin skin (Mantoux) Test", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.623403", "updated_at": "2025-07-08T19:27:20.623406"}, {"id": 80, "test_name": "URINE CHYLURIA", "test_code": "000181", "department": "HAEMATOLOGY", "result_name": "URINE CHYLURIA", "parameter_name": "URINE CHYLURIA", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.623501", "updated_at": "2025-07-08T19:27:20.623504"}, {"id": 81, "test_name": "URINE FAT GLOBULIN", "test_code": "000410", "department": "HAEMATOLOGY", "result_name": "URINE FAT GLOBULIN", "parameter_name": "URINE FAT GLOBULIN", "unit": null, "result_type": "No Unit / Ref. Value", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.623598", "updated_at": "2025-07-08T19:27:20.623600"}, {"id": 82, "test_name": "URINE MYOGLOBIN", "test_code": "000412", "department": "HAEMATOLOGY", "result_name": "URINE MYOGLOBIN", "parameter_name": "URINE MYOGLOBIN", "unit": "ug/L", "result_type": "Pick List", "reference_range": "0 - 1000", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.623695", "updated_at": "2025-07-08T19:27:20.623698"}, {"id": 83, "test_name": "vW (<PERSON>) Factor, Plasma", "test_code": "000677", "department": "HAEMATOLOGY", "result_name": "vW (<PERSON>) Factor, Plasma", "parameter_name": "vW (<PERSON>) Factor, Plasma", "unit": "%", "result_type": "Pick List", "reference_range": "49.50 - 187.00", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Immunoturbidimetry", "specimen_type": "Citrate Plasma", "container": "Citrate Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HAEMATOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.623796", "updated_at": "2025-07-08T19:27:20.623799"}, {"id": 84, "test_name": "ACETONE (URINE)", "test_code": "000315", "department": "CLINICAL_PATHOLOGY", "result_name": "ACETONE (URINE)", "parameter_name": "ACETONE (URINE)", "unit": null, "result_type": "Pick List", "reference_range": "NEGATIVE", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.709263", "updated_at": "2025-07-08T19:27:20.709269"}, {"id": 85, "test_name": "BENCE JONES PROTEIN-URINE", "test_code": "000316", "department": "CLINICAL_PATHOLOGY", "result_name": "BENCE JONES PROTEIN-URINE", "parameter_name": "BENCE JONES PROTEIN-URINE", "unit": null, "result_type": "Pick List", "reference_range": "NEGATIVE", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.709446", "updated_at": "2025-07-08T19:27:20.709450"}, {"id": 86, "test_name": "Bile Pigment", "test_code": "001635", "department": "CLINICAL_PATHOLOGY", "result_name": "Bile Pigment", "parameter_name": "Bile Pigment", "unit": null, "result_type": "Pick List", "reference_range": "Negative", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "URINE", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.709591", "updated_at": "2025-07-08T19:27:20.709595"}, {"id": 87, "test_name": "Bile Salt", "test_code": "001634", "department": "CLINICAL_PATHOLOGY", "result_name": "Bile Salt", "parameter_name": "Bile Salt", "unit": null, "result_type": "Pick List", "reference_range": "Absent", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.709729", "updated_at": "2025-07-08T19:27:20.709732"}, {"id": 88, "test_name": "Cell Count (Body Fluids)", "test_code": "000317", "department": "CLINICAL_PATHOLOGY", "result_name": "Cell Count (Body Fluids)", "parameter_name": "Cell Count (Body Fluids)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Microscopic", "specimen_type": "Body Fluids", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.709867", "updated_at": "2025-07-08T19:27:20.709870"}, {"id": 89, "test_name": "Cell count (CSF)", "test_code": "000313", "department": "CLINICAL_PATHOLOGY", "result_name": "Cell count (CSF)", "parameter_name": "Cell count (CSF)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "CSF", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.710000", "updated_at": "2025-07-08T19:27:20.710003"}, {"id": 90, "test_name": "Chyluria", "test_code": "000311", "department": "CLINICAL_PATHOLOGY", "result_name": "Chyluria", "parameter_name": "Chyluria", "unit": null, "result_type": "Pick List", "reference_range": "Negative", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.710128", "updated_at": "2025-07-08T19:27:20.710131"}, {"id": 91, "test_name": "Color & Transparency", "test_code": "001636", "department": "CLINICAL_PATHOLOGY", "result_name": "Color & Transparency", "parameter_name": "Color & Transparency", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "URINE", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.710254", "updated_at": "2025-07-08T19:27:20.710257"}, {"id": 92, "test_name": "DNA FRAGMENTATION INDEX", "test_code": "001648", "department": "CLINICAL_PATHOLOGY", "result_name": "DNA FRAGMENTATION INDEX", "parameter_name": "DNA FRAGMENTATION INDEX", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.710380", "updated_at": "2025-07-08T19:27:20.710383"}, {"id": 93, "test_name": "FAT GLOBULES (STOOL)", "test_code": "000318", "department": "CLINICAL_PATHOLOGY", "result_name": "FAT GLOBULES (STOOL)", "parameter_name": "FAT GLOBULES (STOOL)", "unit": null, "result_type": "Pick List", "reference_range": "Not Present", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "STOOL", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.710511", "updated_at": "2025-07-08T19:27:20.710514"}, {"id": 94, "test_name": "INTRAUTERINE INSEMINATION -SPERM WASH", "test_code": "001704", "department": "CLINICAL_PATHOLOGY", "result_name": "INTRAUTERINE INSEMINATION -SPERM WASH", "parameter_name": "INTRAUTERINE INSEMINATION -SPERM WASH", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.710636", "updated_at": "2025-07-08T19:27:20.710639"}, {"id": 95, "test_name": "OCCULT BLOOD.", "test_code": "000320", "department": "CLINICAL_PATHOLOGY", "result_name": "OCCULT BLOOD.", "parameter_name": "OCCULT BLOOD.", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Guaiac", "specimen_type": "STOOL", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.710761", "updated_at": "2025-07-08T19:27:20.710765"}, {"id": 96, "test_name": "PREGNANCY TEST-URINE (CARD)", "test_code": "000321", "department": "CLINICAL_PATHOLOGY", "result_name": "PREGNANCY TEST-URINE (CARD)", "parameter_name": "PREGNANCY TEST-URINE (CARD)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.710884", "updated_at": "2025-07-08T19:27:20.710888"}, {"id": 97, "test_name": "REDUCING SUBSTANCE.", "test_code": "000322", "department": "CLINICAL_PATHOLOGY", "result_name": "REDUCING SUBSTANCE.", "parameter_name": "REDUCING SUBSTANCE.", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "MOTION/URINE", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.711009", "updated_at": "2025-07-08T19:27:20.711013"}, {"id": 98, "test_name": "SEMEN ANALYSIS", "test_code": "000323", "department": "CLINICAL_PATHOLOGY", "result_name": "SEMEN ANALYSIS", "parameter_name": "SEMEN ANALYSIS", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "SEMEN", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.711136", "updated_at": "2025-07-08T19:27:20.711140"}, {"id": 99, "test_name": "SEMEN FRUCTOSE", "test_code": "000145", "department": "CLINICAL_PATHOLOGY", "result_name": "SEMEN FRUCTOSE", "parameter_name": "SEMEN FRUCTOSE", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "SEMEN", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.711260", "updated_at": "2025-07-08T19:27:20.711264"}, {"id": 100, "test_name": "SPERM MOTILITY ANALYSIS-SEMEN", "test_code": "001646", "department": "CLINICAL_PATHOLOGY", "result_name": "SPERM MOTILITY ANALYSIS-SEMEN", "parameter_name": "SPERM MOTILITY ANALYSIS-SEMEN", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "SEMEN", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.711383", "updated_at": "2025-07-08T19:27:20.711386"}, {"id": 101, "test_name": "Stool complete examination", "test_code": "000319", "department": "CLINICAL_PATHOLOGY", "result_name": "Stool complete examination", "parameter_name": "Stool complete examination", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "STOOL", "container": "<PERSON><PERSON>tainer", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.711508", "updated_at": "2025-07-08T19:27:20.711511"}, {"id": 102, "test_name": "Stool Ova & Cysts", "test_code": "001654", "department": "CLINICAL_PATHOLOGY", "result_name": "Stool Ova & Cysts", "parameter_name": "Stool Ova & Cysts", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "STOOL", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.711638", "updated_at": "2025-07-08T19:27:20.711641"}, {"id": 103, "test_name": "URINE ACETONE", "test_code": "000408", "department": "CLINICAL_PATHOLOGY", "result_name": "URINE ACETONE", "parameter_name": "URINE ACETONE", "unit": null, "result_type": "Pick List", "reference_range": "NEGATIVE", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.711760", "updated_at": "2025-07-08T19:27:20.711763"}, {"id": 104, "test_name": "URINE ALBUMIN", "test_code": "000325", "department": "CLINICAL_PATHOLOGY", "result_name": "URINE ALBUMIN", "parameter_name": "URINE ALBUMIN", "unit": null, "result_type": "Pick List", "reference_range": "Not present  .", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.711880", "updated_at": "2025-07-08T19:27:20.711883"}, {"id": 105, "test_name": "Urine Chemical Analysis.", "test_code": "001521", "department": "CLINICAL_PATHOLOGY", "result_name": "Urine Chemical Analysis.", "parameter_name": "Urine Chemical Analysis.", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "URINE", "container": "<PERSON><PERSON>tainer", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.712002", "updated_at": "2025-07-08T19:27:20.712006"}, {"id": 106, "test_name": "Urine Complete Examination", "test_code": "000324", "department": "CLINICAL_PATHOLOGY", "result_name": "Urine Complete Examination", "parameter_name": "Urine Complete Examination", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "URINE", "container": "<PERSON><PERSON>tainer", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.712128", "updated_at": "2025-07-08T19:27:20.712132"}, {"id": 107, "test_name": "Urine Dysmorphic RBC", "test_code": "001611", "department": "CLINICAL_PATHOLOGY", "result_name": "Urine Dysmorphic RBC", "parameter_name": "Urine Dysmorphic RBC", "unit": null, "result_type": "Pick List", "reference_range": "Negative", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Microscopic", "specimen_type": "URINE", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.712257", "updated_at": "2025-07-08T19:27:20.712261"}, {"id": 108, "test_name": "URINE OCCULT BLOOD", "test_code": "000413", "department": "CLINICAL_PATHOLOGY", "result_name": "URINE OCCULT BLOOD", "parameter_name": "URINE OCCULT BLOOD", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Guaiac", "specimen_type": "URINE", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.712383", "updated_at": "2025-07-08T19:27:20.712386"}, {"id": 109, "test_name": "Urine Routine Analysis", "test_code": "000414", "department": "CLINICAL_PATHOLOGY", "result_name": "Urine Routine Analysis", "parameter_name": "Urine Routine Analysis", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "URINE", "container": "<PERSON><PERSON>tainer", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.712506", "updated_at": "2025-07-08T19:27:20.712509"}, {"id": 110, "test_name": "URINE SUGAR", "test_code": "000303", "department": "CLINICAL_PATHOLOGY", "result_name": "URINE SUGAR", "parameter_name": "URINE SUGAR", "unit": null, "result_type": "Pick List", "reference_range": "Not Present", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "URINE", "container": "<PERSON><PERSON>tainer", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.712628", "updated_at": "2025-07-08T19:27:20.712632"}, {"id": 111, "test_name": "URINE SUGAR (120 mins)", "test_code": "000307", "department": "CLINICAL_PATHOLOGY", "result_name": "URINE SUGAR (120 mins)", "parameter_name": "URINE SUGAR (120 mins)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "URINE", "container": "URINE Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.712754", "updated_at": "2025-07-08T19:27:20.712758"}, {"id": 112, "test_name": "URINE SUGAR (150 mins)", "test_code": "000309", "department": "CLINICAL_PATHOLOGY", "result_name": "URINE SUGAR (150 mins)", "parameter_name": "URINE SUGAR (150 mins)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "URINE", "container": "<PERSON><PERSON>tainer", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.712880", "updated_at": "2025-07-08T19:27:20.712884"}, {"id": 113, "test_name": "URINE SUGAR (180 mins)", "test_code": "000308", "department": "CLINICAL_PATHOLOGY", "result_name": "URINE SUGAR (180 mins)", "parameter_name": "URINE SUGAR (180 mins)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "URINE", "container": "<PERSON><PERSON>tainer", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.713004", "updated_at": "2025-07-08T19:27:20.713007"}, {"id": 114, "test_name": "URINE SUGAR (30 mins)", "test_code": "000304", "department": "CLINICAL_PATHOLOGY", "result_name": "URINE SUGAR (30 mins)", "parameter_name": "URINE SUGAR (30 mins)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "URINE", "container": "<PERSON><PERSON>tainer", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.713126", "updated_at": "2025-07-08T19:27:20.713129"}, {"id": 115, "test_name": "URINE SUGAR (60 mins)", "test_code": "000305", "department": "CLINICAL_PATHOLOGY", "result_name": "URINE SUGAR (60 mins)", "parameter_name": "URINE SUGAR (60 mins)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "URINE", "container": "<PERSON><PERSON>tainer", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.713249", "updated_at": "2025-07-08T19:27:20.713252"}, {"id": 116, "test_name": "URINE SUGAR (90 mins)", "test_code": "000306", "department": "CLINICAL_PATHOLOGY", "result_name": "URINE SUGAR (90 mins)", "parameter_name": "URINE SUGAR (90 mins)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "URINE", "container": "<PERSON><PERSON>tainer", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.713376", "updated_at": "2025-07-08T19:27:20.713379"}, {"id": 117, "test_name": "URINE SUGAR (F)", "test_code": "000301", "department": "CLINICAL_PATHOLOGY", "result_name": "URINE SUGAR (F)", "parameter_name": "URINE SUGAR (F)", "unit": null, "result_type": "Pick List", "reference_range": "Not Present.", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "URINE", "container": "<PERSON><PERSON>tainer", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.713501", "updated_at": "2025-07-08T19:27:20.713505"}, {"id": 118, "test_name": "URINE SUGAR (PP)", "test_code": "000302", "department": "CLINICAL_PATHOLOGY", "result_name": "URINE SUGAR (PP)", "parameter_name": "URINE SUGAR (PP)", "unit": null, "result_type": "Pick List", "reference_range": "Not Present", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "<PERSON><PERSON>", "container": "<PERSON><PERSON>tainer", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Clinical Pathology", "is_active": true, "created_at": "2025-07-08T19:27:20.713633", "updated_at": "2025-07-08T19:27:20.713636"}, {"id": 119, "test_name": "CHIKUNGUNYA , Qualitative PCR", "test_code": "001098", "department": "MOLECULAR_BIOLOGY", "result_name": "CHIKUNGUNYA , Qualitative PCR", "parameter_name": "CHIKUNGUNYA , Qualitative PCR", "unit": null, "result_type": "Pick List", "reference_range": "NOT DETECTED.", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "PCR", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.799177", "updated_at": "2025-07-08T19:27:20.799184"}, {"id": 120, "test_name": "CHLAMYDIA TRACHOMATIS - PCR Qualitative", "test_code": "001623", "department": "MOLECULAR_BIOLOGY", "result_name": "CHLAMYDIA TRACHOMATIS - PCR Qualitative", "parameter_name": "CHLAMYDIA TRACHOMATIS - PCR Qualitative", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "URINE", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.799412", "updated_at": "2025-07-08T19:27:20.799415"}, {"id": 121, "test_name": "CMV - Cytomegalovirus  PCR (Quantitative)", "test_code": "000923", "department": "MOLECULAR_BIOLOGY", "result_name": "CMV - Cytomegalovirus  PCR (Quantitative)", "parameter_name": "CMV - Cytomegalovirus  PCR (Quantitative)", "unit": "Copies/mL", "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Real Time PCR", "specimen_type": "EDTA PLASMA", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.799544", "updated_at": "2025-07-08T19:27:20.799547"}, {"id": 122, "test_name": "CMV-DNA PCR (Qualitative)", "test_code": "000926", "department": "MOLECULAR_BIOLOGY", "result_name": "CMV-DNA PCR (Qualitative)", "parameter_name": "CMV-DNA PCR (Qualitative)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "RT PCR", "specimen_type": "EDTA PLASMA", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.799653", "updated_at": "2025-07-08T19:27:20.799656"}, {"id": 123, "test_name": "CMV-DNA PCR (Qualitative), Urine", "test_code": "001511", "department": "MOLECULAR_BIOLOGY", "result_name": "CMV-DNA PCR (Qualitative), Urine", "parameter_name": "CMV-DNA PCR (Qualitative), Urine", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "P.C.R", "specimen_type": "URINE", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.799762", "updated_at": "2025-07-08T19:27:20.799765"}, {"id": 124, "test_name": "Cystic Fibrosis Genetic Screening", "test_code": "001653", "department": "MOLECULAR_BIOLOGY", "result_name": "Cystic Fibrosis Genetic Screening", "parameter_name": "Cystic Fibrosis Genetic Screening", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.799867", "updated_at": "2025-07-08T19:27:20.799870"}, {"id": 125, "test_name": "Epstein-Barr Virus (EBV) DNA", "test_code": "000925", "department": "MOLECULAR_BIOLOGY", "result_name": "Epstein-Barr Virus (EBV) DNA", "parameter_name": "Epstein-Barr Virus (EBV) DNA", "unit": "Copies/mL", "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Real Time PCR", "specimen_type": "WHOLE BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.800009", "updated_at": "2025-07-08T19:27:20.800011"}, {"id": 126, "test_name": "HBV - DNA Qualitative", "test_code": "000931", "department": "MOLECULAR_BIOLOGY", "result_name": "HBV - DNA Qualitative", "parameter_name": "HBV - DNA Qualitative", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "PCR METHOD", "specimen_type": "EDTA PLASMA", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.800111", "updated_at": "2025-07-08T19:27:20.800114"}, {"id": 127, "test_name": "HBV DNA - Quantitative", "test_code": "000932", "department": "MOLECULAR_BIOLOGY", "result_name": "HBV DNA - Quantitative", "parameter_name": "HBV DNA - Quantitative", "unit": "IU/ml", "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "PCR", "specimen_type": "EDTA PLASMA", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.800214", "updated_at": "2025-07-08T19:27:20.800217"}, {"id": 128, "test_name": "HCV- RNA PCR (Qualitative)", "test_code": "001132", "department": "MOLECULAR_BIOLOGY", "result_name": "HCV- RNA PCR (Qualitative)", "parameter_name": "HCV- RNA PCR (Qualitative)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Real Time PCR", "specimen_type": "EDTA PLASMA", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.800315", "updated_at": "2025-07-08T19:27:20.800318"}, {"id": 129, "test_name": "HCV- RNA PCR (Quantitative)", "test_code": "001133", "department": "MOLECULAR_BIOLOGY", "result_name": "HCV- RNA PCR (Quantitative)", "parameter_name": "HCV- RNA PCR (Quantitative)", "unit": "IU/ml", "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Real Time P.C.R", "specimen_type": "EDTA PLASMA", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.800416", "updated_at": "2025-07-08T19:27:20.800419"}, {"id": 130, "test_name": "HCV-GENOTYPING", "test_code": "001241", "department": "MOLECULAR_BIOLOGY", "result_name": "HCV-GENOTYPING", "parameter_name": "HCV-GENOTYPING", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "RT-PCR/ Sequencing", "specimen_type": "EDTA PLASMA", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.800538", "updated_at": "2025-07-08T19:27:20.800541"}, {"id": 131, "test_name": "Hepatitis B Genotyping (HBV)", "test_code": "000927", "department": "MOLECULAR_BIOLOGY", "result_name": "Hepatitis B Genotyping (HBV)", "parameter_name": "Hepatitis B Genotyping (HBV)", "unit": null, "result_type": "No Unit / Ref. Value", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.800649", "updated_at": "2025-07-08T19:27:20.800651"}, {"id": 132, "test_name": "HIV  RNA (Quantitative)", "test_code": "000933", "department": "MOLECULAR_BIOLOGY", "result_name": "HIV  RNA (Quantitative)", "parameter_name": "HIV  RNA (Quantitative)", "unit": "IU/ml", "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Real Time PCR", "specimen_type": "EDTA PLASMA", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.800750", "updated_at": "2025-07-08T19:27:20.800752"}, {"id": 133, "test_name": "HIV-1 Drug Resistance – Extended (PI, NRTI, NNRTI, INSTI)", "test_code": "001535", "department": "MOLECULAR_BIOLOGY", "result_name": "HIV-1 Drug Resistance – Extended (PI, NRTI, NNRTI, INSTI)", "parameter_name": "HIV-1 Drug Resistance – Extended (PI, NRTI, NNRTI, INSTI)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "RT-PCR/ Sequencing", "specimen_type": "EDTA PLASMA", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.800848", "updated_at": "2025-07-08T19:27:20.800850"}, {"id": 134, "test_name": "HIV-1 Proviral DNA (Qualitative)", "test_code": "000929", "department": "MOLECULAR_BIOLOGY", "result_name": "HIV-1 Proviral DNA (Qualitative)", "parameter_name": "HIV-1 Proviral DNA (Qualitative)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "PCR", "specimen_type": "EDTA BLOOD", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.800945", "updated_at": "2025-07-08T19:27:20.800947"}, {"id": 135, "test_name": "HIV-RNA QUALITATIVE", "test_code": "000934", "department": "MOLECULAR_BIOLOGY", "result_name": "HIV-RNA QUALITATIVE", "parameter_name": "HIV-RNA QUALITATIVE", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "P.C.R", "specimen_type": "EDTA PLASMA", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.801064", "updated_at": "2025-07-08T19:27:20.801067"}, {"id": 136, "test_name": "HLA B27 (PCR)", "test_code": "001356", "department": "MOLECULAR_BIOLOGY", "result_name": "HLA B27 (PCR)", "parameter_name": "HLA B27 (PCR)", "unit": null, "result_type": "Pick List", "reference_range": "NOT DETECTED\nNegative", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "P.C.R", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.801179", "updated_at": "2025-07-08T19:27:20.801182"}, {"id": 137, "test_name": "HSV DNA (1 & 2) QUALITATIVE PCR", "test_code": "001230", "department": "MOLECULAR_BIOLOGY", "result_name": "HSV DNA (1 & 2) QUALITATIVE PCR", "parameter_name": "HSV DNA (1 & 2) QUALITATIVE PCR", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.801274", "updated_at": "2025-07-08T19:27:20.801277"}, {"id": 138, "test_name": "Human Papillomavirus  DNA - PCR", "test_code": "001235", "department": "MOLECULAR_BIOLOGY", "result_name": "Human Papillomavirus  DNA - PCR", "parameter_name": "Human Papillomavirus  DNA - PCR", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.801370", "updated_at": "2025-07-08T19:27:20.801373"}, {"id": 139, "test_name": "JAPANESE ENCEPHALITIS VIRUS DETECTION, PCR", "test_code": "001144", "department": "MOLECULAR_BIOLOGY", "result_name": "JAPANESE ENCEPHALITIS VIRUS DETECTION, PCR", "parameter_name": "JAPANESE ENCEPHALITIS VIRUS DETECTION, PCR", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "RT PCR", "specimen_type": "Serum/CSF", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.801467", "updated_at": "2025-07-08T19:27:20.801470"}, {"id": 140, "test_name": "Malaria Parasite , PCR", "test_code": "001527", "department": "MOLECULAR_BIOLOGY", "result_name": "Malaria Parasite , PCR", "parameter_name": "Malaria Parasite , PCR", "unit": null, "result_type": "Pick List", "reference_range": "Not Detected", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "PCR", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.801566", "updated_at": "2025-07-08T19:27:20.801568"}, {"id": 141, "test_name": "MTHFR Mutations", "test_code": "000928", "department": "MOLECULAR_BIOLOGY", "result_name": "MTHFR Mutations", "parameter_name": "MTHFR Mutations", "unit": null, "result_type": "Template", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "PCR- SNPE", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.801700", "updated_at": "2025-07-08T19:27:20.801703"}, {"id": 142, "test_name": "NEISSERIA GONORRHOEA", "test_code": "001735", "department": "MOLECULAR_BIOLOGY", "result_name": "NEISSERIA GONORRHOEA", "parameter_name": "NEISSERIA GONORRHOEA", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "PCR", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.801798", "updated_at": "2025-07-08T19:27:20.801800"}, {"id": 143, "test_name": "Neurotropic Virus panel, PCR", "test_code": "001534", "department": "MOLECULAR_BIOLOGY", "result_name": "Neurotropic Virus panel, PCR", "parameter_name": "Neurotropic Virus panel, PCR", "unit": null, "result_type": "Template", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.801894", "updated_at": "2025-07-08T19:27:20.801896"}, {"id": 144, "test_name": "Parvovirus B19, DNA PCR", "test_code": "001322", "department": "MOLECULAR_BIOLOGY", "result_name": "Parvovirus B19, DNA PCR", "parameter_name": "Parvovirus B19, DNA PCR", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Real Time PCR", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.801991", "updated_at": "2025-07-08T19:27:20.801993"}, {"id": 145, "test_name": "PML-RARA GENE REARRANGEMENT, QUALITATIVE", "test_code": "001626", "department": "MOLECULAR_BIOLOGY", "result_name": "PML-RARA GENE REARRANGEMENT, QUALITATIVE", "parameter_name": "PML-RARA GENE REARRANGEMENT, QUALITATIVE", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "PCR", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.802246", "updated_at": "2025-07-08T19:27:20.802252"}, {"id": 146, "test_name": "SARS-CoV-2 (COVID-19)", "test_code": "001566", "department": "MOLECULAR_BIOLOGY", "result_name": "SARS-CoV-2 (COVID-19)", "parameter_name": "SARS-CoV-2 (COVID-19)", "unit": null, "result_type": "Template", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Real Time P.C.R", "specimen_type": null, "container": "Viral Transport Media", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.802828", "updated_at": "2025-07-08T19:27:20.802835"}, {"id": 147, "test_name": "TB PCR (BLOOD)", "test_code": "001184", "department": "MOLECULAR_BIOLOGY", "result_name": "TB PCR (BLOOD)", "parameter_name": "TB PCR (BLOOD)", "unit": null, "result_type": "No Unit / Ref. Value", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "PCR", "specimen_type": "BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.803280", "updated_at": "2025-07-08T19:27:20.803286"}, {"id": 148, "test_name": "TB PCR (<PERSON>.<PERSON> by Gene<PERSON>pert)", "test_code": "000935", "department": "MOLECULAR_BIOLOGY", "result_name": "TB PCR (<PERSON>.<PERSON> by Gene<PERSON>pert)", "parameter_name": "TB PCR (<PERSON>.<PERSON> by Gene<PERSON>pert)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.803662", "updated_at": "2025-07-08T19:27:20.803667"}, {"id": 149, "test_name": "THALASSEMIA - BETA (5 COMMON MUTATION),BLOOD", "test_code": "000924", "department": "MOLECULAR_BIOLOGY", "result_name": "THALASSEMIA - BETA (5 COMMON MUTATION),BLOOD", "parameter_name": "THALASSEMIA - BETA (5 COMMON MUTATION),BLOOD", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.803847", "updated_at": "2025-07-08T19:27:20.803851"}, {"id": 150, "test_name": "TOXOPLASMA DNA PCR", "test_code": "001228", "department": "MOLECULAR_BIOLOGY", "result_name": "TOXOPLASMA DNA PCR", "parameter_name": "TOXOPLASMA DNA PCR", "unit": null, "result_type": "No Unit / Ref. Value", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "PCR", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.804046", "updated_at": "2025-07-08T19:27:20.804054"}, {"id": 151, "test_name": "Y Chromosome Microdeletion", "test_code": "000930", "department": "MOLECULAR_BIOLOGY", "result_name": "Y Chromosome Microdeletion", "parameter_name": "Y Chromosome Microdeletion", "unit": null, "result_type": "Template", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Multiplex PCR", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Molecular Biology", "is_active": true, "created_at": "2025-07-08T19:27:20.804236", "updated_at": "2025-07-08T19:27:20.804239"}, {"id": 152, "test_name": "HOMA IR (INSULIN RESISTANCE)", "test_code": "001668", "department": "ENDOCRINOLOGY", "result_name": "HOMA IR (INSULIN RESISTANCE)", "parameter_name": "HOMA IR (INSULIN RESISTANCE)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "BLOOD", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "ENDOCRINOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.883560", "updated_at": "2025-07-08T19:27:20.883566"}, {"id": 153, "test_name": "KARYOTYPING- COUPLES", "test_code": "001677", "department": "ENDOCRINOLOGY", "result_name": "KARYOTYPING- COUPLES", "parameter_name": "KARYOTYPING- COUPLES", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "Citrate Plasma", "container": "Citrate Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "ENDOCRINOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.883720", "updated_at": "2025-07-08T19:27:20.883726"}, {"id": 154, "test_name": "KARYOTYPING- SINGLE", "test_code": "001676", "department": "ENDOCRINOLOGY", "result_name": "KARYOTYPING- SINGLE", "parameter_name": "KARYOTYPING- SINGLE", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "Citrate Plasma", "container": "Citrate Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "ENDOCRINOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.883843", "updated_at": "2025-07-08T19:27:20.883846"}, {"id": 155, "test_name": "Biopsy Large", "test_code": "001652", "department": "HISTOPATHOLOGY", "result_name": "Biopsy Large", "parameter_name": "Biopsy Large", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HISTOPATHOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.985364", "updated_at": "2025-07-08T19:27:20.985369"}, {"id": 156, "test_name": "Biopsy Medium", "test_code": "001651", "department": "HISTOPATHOLOGY", "result_name": "Biopsy Medium", "parameter_name": "Biopsy Medium", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HISTOPATHOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.987583", "updated_at": "2025-07-08T19:27:20.987616"}, {"id": 157, "test_name": "Biopsy Small", "test_code": "001352", "department": "HISTOPATHOLOGY", "result_name": "Biopsy Small", "parameter_name": "Biopsy Small", "unit": null, "result_type": "Template", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HISTOPATHOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.988673", "updated_at": "2025-07-08T19:27:20.988681"}, {"id": 158, "test_name": "Biopsy Small-1", "test_code": "001650", "department": "HISTOPATHOLOGY", "result_name": "Biopsy Small-1", "parameter_name": "Biopsy Small-1", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HISTOPATHOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.988990", "updated_at": "2025-07-08T19:27:20.988994"}, {"id": 159, "test_name": "CYTOLOGY", "test_code": "001736", "department": "HISTOPATHOLOGY", "result_name": "CYTOLOGY", "parameter_name": "CYTOLOGY", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HISTOPATHOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.989154", "updated_at": "2025-07-08T19:27:20.989157"}, {"id": 160, "test_name": "FNAC", "test_code": "001647", "department": "HISTOPATHOLOGY", "result_name": "FNAC", "parameter_name": "FNAC", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "FLUID", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HISTOPATHOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.989269", "updated_at": "2025-07-08T19:27:20.989271"}, {"id": 161, "test_name": "HER 2/ NEU, FISH", "test_code": "001619", "department": "HISTOPATHOLOGY", "result_name": "HER 2/ NEU, FISH", "parameter_name": "HER 2/ NEU, FISH", "unit": null, "result_type": "Template", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HISTOPATHOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.989392", "updated_at": "2025-07-08T19:27:20.989395"}, {"id": 162, "test_name": "Her-2/Neu(C-Er-B2)(ERBB2)by FISH", "test_code": "001505", "department": "HISTOPATHOLOGY", "result_name": "Her-2/Neu(C-Er-B2)(ERBB2)by FISH", "parameter_name": "Her-2/Neu(C-Er-B2)(ERBB2)by FISH", "unit": null, "result_type": "Template", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HISTOPATHOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.989547", "updated_at": "2025-07-08T19:27:20.989551"}, {"id": 163, "test_name": "IMMUNOHISTOCHEMISTRY", "test_code": "001430", "department": "HISTOPATHOLOGY", "result_name": "IMMUNOHISTOCHEMISTRY", "parameter_name": "IMMUNOHISTOCHEMISTRY", "unit": null, "result_type": "Template", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HISTOPATHOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.989685", "updated_at": "2025-07-08T19:27:20.989688"}, {"id": 164, "test_name": "LIQUID BASED CYTOLOGY", "test_code": "001301", "department": "HISTOPATHOLOGY", "result_name": "LIQUID BASED CYTOLOGY", "parameter_name": "LIQUID BASED CYTOLOGY", "unit": null, "result_type": "Template", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HISTOPATHOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.989806", "updated_at": "2025-07-08T19:27:20.989808"}, {"id": 165, "test_name": "MF ON QBC.", "test_code": "000449", "department": "HISTOPATHOLOGY", "result_name": "MF ON QBC.", "parameter_name": "MF ON QBC.", "unit": null, "result_type": "No Unit / Ref. Value", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HISTOPATHOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.989906", "updated_at": "2025-07-08T19:27:20.989909"}, {"id": 166, "test_name": "PAP - SMEAR", "test_code": "001299", "department": "HISTOPATHOLOGY", "result_name": "PAP - SMEAR", "parameter_name": "PAP - SMEAR", "unit": null, "result_type": "Template", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "PAP SMEAR", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "HISTOPATHOLOGY", "is_active": true, "created_at": "2025-07-08T19:27:20.990007", "updated_at": "2025-07-08T19:27:20.990010"}, {"id": 167, "test_name": "Allergen - <PERSON><PERSON><PERSON><PERSON> (Fungus)", "test_code": "001040", "department": "SEROLOGY", "result_name": "Allergen - <PERSON><PERSON><PERSON><PERSON> (Fungus)", "parameter_name": "Allergen - <PERSON><PERSON><PERSON><PERSON> (Fungus)", "unit": "kUA/L", "result_type": "Numeric", "reference_range": "Negative : < 0.1 Positive : >=0.1", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": null, "specimen_type": "Serum", "container": "Serum Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.121991", "updated_at": "2025-07-08T19:27:21.121996"}, {"id": 168, "test_name": "Allergen - Gluten", "test_code": "001350", "department": "SEROLOGY", "result_name": "Allergen - Gluten", "parameter_name": "Allergen - Gluten", "unit": "kUA/L", "result_type": "Numeric", "reference_range": "NEGATIVE  : < 0.1 POSITIVE  : >= 0.1", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "ImmunoCAP (Fluoroenzymeimmunoassay)- Specific IgE", "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.122116", "updated_at": "2025-07-08T19:27:21.122119"}, {"id": 169, "test_name": "Allergen - Milk", "test_code": "001287", "department": "SEROLOGY", "result_name": "Allergen - Milk", "parameter_name": "Allergen - Milk", "unit": "kUA/L", "result_type": "Pick List", "reference_range": "Negative: < 0.1 Positive: >= 0.1", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ImmunoCAP (Fluoroenzymeimmunoassay)- Specific IgE", "specimen_type": "SERUM", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.122281", "updated_at": "2025-07-08T19:27:21.122284"}, {"id": 170, "test_name": "Allergen EGG White", "test_code": "001290", "department": "SEROLOGY", "result_name": "Allergen EGG White", "parameter_name": "Allergen EGG White", "unit": "kUA/L", "result_type": "Pick List", "reference_range": "Less than 0.1 : Not Detectable  0.10 - 0.50   : Very Low  0.50 - 2.00   : Low  2.00 - 15.0   : Moderate  15.0 - 50.0   : High  >50.0         : Very High.", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "FEIA", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.122403", "updated_at": "2025-07-08T19:27:21.122406"}, {"id": 171, "test_name": "Allergen EGG Yolk", "test_code": "001289", "department": "SEROLOGY", "result_name": "Allergen EGG Yolk", "parameter_name": "Allergen EGG Yolk", "unit": "kUA/L", "result_type": "Numeric", "reference_range": "Negative: < 0.1 Positive: >= 0.1", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ImmunoCAP (Fluoroenzymeimmunoassay)- Specific IgE", "specimen_type": "SERUM", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.122508", "updated_at": "2025-07-08T19:27:21.122510"}, {"id": 172, "test_name": "Allergens Food - Nuts", "test_code": "001288", "department": "SEROLOGY", "result_name": "Allergens Food - Nuts", "parameter_name": "Allergens Food - Nuts", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.122609", "updated_at": "2025-07-08T19:27:21.122611"}, {"id": 173, "test_name": "Allergy Profile (Drugs)", "test_code": "001285", "department": "SEROLOGY", "result_name": "Allergy Profile (Drugs)", "parameter_name": "Allergy Profile (Drugs)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.122711", "updated_at": "2025-07-08T19:27:21.122713"}, {"id": 174, "test_name": "ALLERGY PROFILE- DRUG ALLERGY (22 Approx. Test)", "test_code": "001658", "department": "SEROLOGY", "result_name": "ALLERGY PROFILE- DRUG ALLERGY (22 Approx. Test)", "parameter_name": "ALLERGY PROFILE- DRUG ALLERGY (22 Approx. Test)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.122847", "updated_at": "2025-07-08T19:27:21.122849"}, {"id": 175, "test_name": "ALLERGY PROFILE- INHALANT WITH CONTACTS (100 Approx. Test)", "test_code": "001657", "department": "SEROLOGY", "result_name": "ALLERGY PROFILE- INHALANT WITH CONTACTS (100 Approx. Test)", "parameter_name": "ALLERGY PROFILE- INHALANT WITH CONTACTS (100 Approx. Test)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.122945", "updated_at": "2025-07-08T19:27:21.122948"}, {"id": 176, "test_name": "ALLERGY PROFILE- NON-VEG FOOD ONLY (14 Approx. Test)", "test_code": "001656", "department": "SEROLOGY", "result_name": "ALLERGY PROFILE- NON-VEG FOOD ONLY (14 Approx. Test)", "parameter_name": "ALLERGY PROFILE- NON-VEG FOOD ONLY (14 Approx. Test)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.123042", "updated_at": "2025-07-08T19:27:21.123044"}, {"id": 177, "test_name": "ALLERGY PROFILE- VEG FOOD ONLY (83 Approx.Test)", "test_code": "001655", "department": "SEROLOGY", "result_name": "ALLERGY PROFILE- VEG FOOD ONLY (83 Approx.Test)", "parameter_name": "ALLERGY PROFILE- VEG FOOD ONLY (83 Approx.Test)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.123138", "updated_at": "2025-07-08T19:27:21.123141"}, {"id": 178, "test_name": "ALLERGY PROFILE- VEG FOOD+ INHALANTS + CONTACTS (183 Approx. Test)", "test_code": "001659", "department": "SEROLOGY", "result_name": "ALLERGY PROFILE- VEG FOOD+ INHALANTS + CONTACTS (183 Approx. Test)", "parameter_name": "ALLERGY PROFILE- VEG FOOD+ INHALANTS + CONTACTS (183 Approx. Test)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.123234", "updated_at": "2025-07-08T19:27:21.123236"}, {"id": 179, "test_name": "ALLERGY PROFILE- VEG FOOD+ INHALANTS WITH CONTACTS+ DRUGS (205 Approx. Test)", "test_code": "001660", "department": "SEROLOGY", "result_name": "ALLERGY PROFILE- VEG FOOD+ INHALANTS WITH CONTACTS+ DRUGS (205 Approx. Test)", "parameter_name": "ALLERGY PROFILE- VEG FOOD+ INHALANTS WITH CONTACTS+ DRUGS (205 Approx. Test)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.123353", "updated_at": "2025-07-08T19:27:21.123368"}, {"id": 180, "test_name": "ALLERGY PROFILE- VEG FOOD+ INHALANTS WITH CONTACTS+ NON-VEG (197 Approx. Test)", "test_code": "001661", "department": "SEROLOGY", "result_name": "ALLERGY PROFILE- VEG FOOD+ INHALANTS WITH CONTACTS+ NON-VEG (197 Approx. Test)", "parameter_name": "ALLERGY PROFILE- VEG FOOD+ INHALANTS WITH CONTACTS+ NON-VEG (197 Approx. Test)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.123462", "updated_at": "2025-07-08T19:27:21.123465"}, {"id": 181, "test_name": "Allergy Screening - <PERSON><PERSON><PERSON>", "test_code": "001304", "department": "SEROLOGY", "result_name": "Allergy Screening - <PERSON><PERSON><PERSON>", "parameter_name": "Allergy Screening - <PERSON><PERSON><PERSON>", "unit": "ug/L", "result_type": "Pick List", "reference_range": "<= 11.0", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Immunocap", "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.123560", "updated_at": "2025-07-08T19:27:21.123562"}, {"id": 182, "test_name": "AMOEBIC ANTIBODY- IgG", "test_code": "001041", "department": "SEROLOGY", "result_name": "AMOEBIC ANTIBODY- IgG", "parameter_name": "AMOEBIC ANTIBODY- IgG", "unit": "NTU", "result_type": "Numeric", "reference_range": "Negative - 0  to 0.4  Positive - Above 0.4", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "EIA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.123657", "updated_at": "2025-07-08T19:27:21.123660"}, {"id": 183, "test_name": "ANA", "test_code": "001042", "department": "SEROLOGY", "result_name": "ANA", "parameter_name": "ANA", "unit": "OD RATIO", "result_type": null, "reference_range": "NEGATIVE : <1.0 Equivocal: 1.0 - 1.2 POSITIVE : >1.2", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "E.L.I.S.A", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.123756", "updated_at": "2025-07-08T19:27:21.123759"}, {"id": 184, "test_name": "ANA INDEX", "test_code": "001044", "department": "SEROLOGY", "result_name": "ANA INDEX", "parameter_name": "ANA INDEX", "unit": "AI", "result_type": "Numeric", "reference_range": "<1 AI NEGATIVE  1 -1.2 AI BORDERLINE  >1.2 AI POSITIVE", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "ELISA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.123875", "updated_at": "2025-07-08T19:27:21.123878"}, {"id": 185, "test_name": "ANA PROFILE (Blot)", "test_code": "001227", "department": "SEROLOGY", "result_name": "ANA PROFILE (Blot)", "parameter_name": "ANA PROFILE (Blot)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "BLOT", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.123988", "updated_at": "2025-07-08T19:27:21.123990"}, {"id": 186, "test_name": "ANCA", "test_code": "001035", "department": "SEROLOGY", "result_name": "ANCA", "parameter_name": "ANCA", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "Serum", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.124084", "updated_at": "2025-07-08T19:27:21.124086"}, {"id": 187, "test_name": "ANTI  HBe", "test_code": "001047", "department": "SEROLOGY", "result_name": "ANTI  HBe", "parameter_name": "ANTI  HBe", "unit": "S/Co", "result_type": null, "reference_range": ">= 1.11     : NEGATIVE  1.01 - 1.10 : EQUIVOCAL  <= 1.01     : POSITIVE", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "CMIA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.124181", "updated_at": "2025-07-08T19:27:21.124183"}, {"id": 188, "test_name": "ANTI  LEPTOSPIRAL ANTIBODY IgM", "test_code": "001048", "department": "SEROLOGY", "result_name": "ANTI  LEPTOSPIRAL ANTIBODY IgM", "parameter_name": "ANTI  LEPTOSPIRAL ANTIBODY IgM", "unit": "PB Units", "result_type": null, "reference_range": "NEGATIVE - <9.0 PB Units  BORDERLINE - 9.0 - 11.0 PB Units  POSITIVE - >11.0 PB Units", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELISA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.124276", "updated_at": "2025-07-08T19:27:21.124279"}, {"id": 189, "test_name": "ANTI CARDIO LIPIN ANTIBODY IgA", "test_code": "001052", "department": "SEROLOGY", "result_name": "ANTI CARDIO LIPIN ANTIBODY IgA", "parameter_name": "ANTI CARDIO LIPIN ANTIBODY IgA", "unit": "APLU/ML", "result_type": "Pick List", "reference_range": "<12.0 : Negative  12-18 : Equivocal  >18.0 : <PERSON>sitive", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELISA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.124371", "updated_at": "2025-07-08T19:27:21.124374"}, {"id": 190, "test_name": "ANTI CARDIOLIPIN ANTIBODY IgG", "test_code": "001053", "department": "SEROLOGY", "result_name": "ANTI CARDIOLIPIN ANTIBODY IgG", "parameter_name": "ANTI CARDIOLIPIN ANTIBODY IgG", "unit": "PL IgG-U/mL", "result_type": null, "reference_range": "Less than 12", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "ELISA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.124504", "updated_at": "2025-07-08T19:27:21.124506"}, {"id": 191, "test_name": "ANTI CARDIOLIPIN ANTIBODY IgM", "test_code": "001054", "department": "SEROLOGY", "result_name": "ANTI CARDIOLIPIN ANTIBODY IgM", "parameter_name": "ANTI CARDIOLIPIN ANTIBODY IgM", "unit": "PL IgG-U/mL", "result_type": null, "reference_range": "Less than 12", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "ELISA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.124600", "updated_at": "2025-07-08T19:27:21.124603"}, {"id": 192, "test_name": "ANTI CCP", "test_code": "001055", "department": "SEROLOGY", "result_name": "ANTI CCP", "parameter_name": "ANTI CCP", "unit": "U/mL", "result_type": null, "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "ECLIA", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.124698", "updated_at": "2025-07-08T19:27:21.124701"}, {"id": 193, "test_name": "ANTI DIURETIC HORMONE-ADH", "test_code": "001233", "department": "SEROLOGY", "result_name": "ANTI DIURETIC HORMONE-ADH", "parameter_name": "ANTI DIURETIC HORMONE-ADH", "unit": "pg/ml", "result_type": "Pick List", "reference_range": "30 - 150", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELISA", "specimen_type": "EDTA PLASMA", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.124795", "updated_at": "2025-07-08T19:27:21.124798"}, {"id": 194, "test_name": "Anti DNase B", "test_code": "001208", "department": "SEROLOGY", "result_name": "Anti DNase B", "parameter_name": "Anti DNase B", "unit": "U/mL", "result_type": "Pick List", "reference_range": "Less than 200", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "NEPHELOMETRY", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.124891", "updated_at": "2025-07-08T19:27:21.124894"}, {"id": 195, "test_name": "Anti dsDNA", "test_code": "001046", "department": "SEROLOGY", "result_name": "Anti dsDNA", "parameter_name": "Anti dsDNA", "unit": "OD/Ratio", "result_type": "Pick List", "reference_range": "Negative : <1 Positive : >=1", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELISA", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.125011", "updated_at": "2025-07-08T19:27:21.125014"}, {"id": 196, "test_name": "Anti dsDNA, IFA", "test_code": "001558", "department": "SEROLOGY", "result_name": "Anti dsDNA, IFA", "parameter_name": "Anti dsDNA, IFA", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "1:10 by Immunofluorescence", "specimen_type": "SERUM", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.125111", "updated_at": "2025-07-08T19:27:21.125114"}, {"id": 197, "test_name": "ANTI GLIADIN IgA", "test_code": "001214", "department": "SEROLOGY", "result_name": "ANTI GLIADIN IgA", "parameter_name": "ANTI GLIADIN IgA", "unit": "units", "result_type": null, "reference_range": "<25  : Negative >=25 : Positive", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELISA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.125209", "updated_at": "2025-07-08T19:27:21.125212"}, {"id": 198, "test_name": "ANTI GLIADIN IgG", "test_code": "001215", "department": "SEROLOGY", "result_name": "ANTI GLIADIN IgG", "parameter_name": "ANTI GLIADIN IgG", "unit": "units", "result_type": null, "reference_range": "<20     : Negative 20 – 30 : <PERSON><PERSON> >30     : <PERSON>sitive", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELISA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.125309", "updated_at": "2025-07-08T19:27:21.125312"}, {"id": 199, "test_name": "ANTI HAV - IgG", "test_code": "001058", "department": "SEROLOGY", "result_name": "ANTI HAV - IgG", "parameter_name": "ANTI HAV - IgG", "unit": "S/Co", "result_type": "Pick List", "reference_range": "<1.0  : Nonreactive >= 1.0: Reactive", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "CMIA", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.125410", "updated_at": "2025-07-08T19:27:21.125412"}, {"id": 200, "test_name": "ANTI HAV -TOTAL", "test_code": "001059", "department": "SEROLOGY", "result_name": "ANTI HAV -TOTAL", "parameter_name": "ANTI HAV -TOTAL", "unit": "OD RATIO", "result_type": "Pick List", "reference_range": "Negative : < 0.90 Equivocal: 0.90 - 1.10 Positive : > 1.10.", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELISA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.125512", "updated_at": "2025-07-08T19:27:21.125515"}, {"id": 201, "test_name": "ANTI HAV IgM", "test_code": "001060", "department": "SEROLOGY", "result_name": "ANTI HAV IgM", "parameter_name": "ANTI HAV IgM", "unit": "COI", "result_type": null, "reference_range": "<1.0  : Non-Reactive >= 1.0: Reactive.", "critical_low": null, "critical_high": null, "decimal_places": 3, "method": "ECLIA", "specimen_type": "Serum", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.125614", "updated_at": "2025-07-08T19:27:21.125617"}, {"id": 202, "test_name": "ANTI HBc IgM", "test_code": "001063", "department": "SEROLOGY", "result_name": "ANTI HBc IgM", "parameter_name": "ANTI HBc IgM", "unit": "PEIU/ml", "result_type": "Pick List", "reference_range": "<5         : Negative >=5 to <10 : Equivocal >=10       : Positive", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELFA", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.125716", "updated_at": "2025-07-08T19:27:21.125718"}, {"id": 203, "test_name": "ANTI HBc Total", "test_code": "001062", "department": "SEROLOGY", "result_name": "ANTI HBc Total", "parameter_name": "ANTI HBc Total", "unit": "index", "result_type": "Pick List", "reference_range": "<1.0          : Positive <=1.0 to <1.4 : Equivocal >=1.4         : Negative  (Competitive Assay, Lower value indicates Positivity)", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELFA", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.125815", "updated_at": "2025-07-08T19:27:21.125818"}, {"id": 204, "test_name": "ANTI HBe", "test_code": "001209", "department": "SEROLOGY", "result_name": "ANTI HBe", "parameter_name": "ANTI HBe", "unit": "S/Co", "result_type": "Pick List", "reference_range": "REACTIVE      : <=1.00 NON REACTIVE  : >1.00  (Competitive Assay, Lower value indicates Positivity)", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "CMIA", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.125917", "updated_at": "2025-07-08T19:27:21.125920"}, {"id": 205, "test_name": "ANTI HBs", "test_code": "001064", "department": "SEROLOGY", "result_name": "ANTI HBs", "parameter_name": "ANTI HBs", "unit": "uIU/ml", "result_type": null, "reference_range": "<10.0  NON IMMUNE", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "CMIA", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.126017", "updated_at": "2025-07-08T19:27:21.126020"}, {"id": 206, "test_name": "ANTI HCV", "test_code": "001065", "department": "SEROLOGY", "result_name": "ANTI HCV", "parameter_name": "ANTI HCV", "unit": "S/Co", "result_type": "Pick List", "reference_range": "< 1.0 : Non Reactive > 1.0 : Reactive", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "CMIA", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.126123", "updated_at": "2025-07-08T19:27:21.126126"}, {"id": 207, "test_name": "ANTI HCV (CARD)", "test_code": "001211", "department": "SEROLOGY", "result_name": "ANTI HCV (CARD)", "parameter_name": "ANTI HCV (CARD)", "unit": null, "result_type": "Pick List", "reference_range": "NEGATIVE", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IC", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.126226", "updated_at": "2025-07-08T19:27:21.126229"}, {"id": 208, "test_name": "ANTI HCV (ELISA)", "test_code": "001303", "department": "SEROLOGY", "result_name": "ANTI HCV (ELISA)", "parameter_name": "ANTI HCV (ELISA)", "unit": "OD Ratio", "result_type": "Pick List", "reference_range": "Negative : < Positive : >=", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "E.L.I.S.A", "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.126327", "updated_at": "2025-07-08T19:27:21.126330"}, {"id": 209, "test_name": "Anti HCV IGG", "test_code": "001702", "department": "SEROLOGY", "result_name": "Anti HCV IGG", "parameter_name": "Anti HCV IGG", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.126425", "updated_at": "2025-07-08T19:27:21.126428"}, {"id": 210, "test_name": "ANTI HCV IgM", "test_code": "001067", "department": "SEROLOGY", "result_name": "ANTI HCV IgM", "parameter_name": "ANTI HCV IgM", "unit": "index", "result_type": null, "reference_range": "\n< 1 : Negative >1  : Positive\n", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.126524", "updated_at": "2025-07-08T19:27:21.126527"}, {"id": 211, "test_name": "ANTI HDV IgM", "test_code": "001223", "department": "SEROLOGY", "result_name": "ANTI HDV IgM", "parameter_name": "ANTI HDV IgM", "unit": "index", "result_type": "Pick List", "reference_range": "< 0.9         :  Negative 0.9 - 1.1     :  Equivocal > 1.1         :  Positive", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "EIA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.126629", "updated_at": "2025-07-08T19:27:21.126631"}, {"id": 212, "test_name": "ANTI HEV IgG", "test_code": "001068", "department": "SEROLOGY", "result_name": "ANTI HEV IgG", "parameter_name": "ANTI HEV IgG", "unit": "index", "result_type": "Pick List", "reference_range": "NEGATIVE : <0.9 Equivocal: 0.9 - 1.1 POSITIVE : >1.1", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "E.I.A", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.126728", "updated_at": "2025-07-08T19:27:21.126730"}, {"id": 213, "test_name": "ANTI HEV IgM", "test_code": "001069", "department": "SEROLOGY", "result_name": "ANTI HEV IgM", "parameter_name": "ANTI HEV IgM", "unit": "COI", "result_type": "Pick List", "reference_range": "Negative: <0.9 Equivocal: 0.9 - 1.1 Positive: >1.1", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELISA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.126827", "updated_at": "2025-07-08T19:27:21.126830"}, {"id": 214, "test_name": "ANTI HIV 1&2 (CARD)", "test_code": "001071", "department": "SEROLOGY", "result_name": "ANTI HIV 1&2 (CARD)", "parameter_name": "ANTI HIV 1&2 (CARD)", "unit": null, "result_type": "Pick List", "reference_range": "Non Reactive", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IC", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.126926", "updated_at": "2025-07-08T19:27:21.126929"}, {"id": 215, "test_name": "ANTI HIV Ag/Ab Combo", "test_code": "001070", "department": "SEROLOGY", "result_name": "ANTI HIV Ag/Ab Combo", "parameter_name": "ANTI HIV Ag/Ab Combo", "unit": "COI", "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.127025", "updated_at": "2025-07-08T19:27:21.127027"}, {"id": 216, "test_name": "ANTI HIV Ag/Ab Combo.", "test_code": "001544", "department": "SEROLOGY", "result_name": "ANTI HIV Ag/Ab Combo.", "parameter_name": "ANTI HIV Ag/Ab Combo.", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "SERUM", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.127126", "updated_at": "2025-07-08T19:27:21.127128"}, {"id": 217, "test_name": "ANTI HIV I & 2 (ELISA)", "test_code": "001210", "department": "SEROLOGY", "result_name": "ANTI HIV I & 2 (ELISA)", "parameter_name": "ANTI HIV I & 2 (ELISA)", "unit": "OD RATIO", "result_type": "Pick List", "reference_range": "NEGATIVE : < POSITIVE : >=", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELISA", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.127226", "updated_at": "2025-07-08T19:27:21.127228"}, {"id": 218, "test_name": "Anti MAG (Myelin associated Glycoprotein)", "test_code": "001283", "department": "SEROLOGY", "result_name": "Anti MAG (Myelin associated Glycoprotein)", "parameter_name": "Anti MAG (Myelin associated Glycoprotein)", "unit": null, "result_type": "Pick List", "reference_range": "NEGATIVE", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "BLOT - IMMUNOASSAY IgM", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.127323", "updated_at": "2025-07-08T19:27:21.127326"}, {"id": 219, "test_name": "ANTI MALARIAL ANTIBODY IgG", "test_code": "001075", "department": "SEROLOGY", "result_name": "ANTI MALARIAL ANTIBODY IgG", "parameter_name": "ANTI MALARIAL ANTIBODY IgG", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IC", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.127420", "updated_at": "2025-07-08T19:27:21.127423"}, {"id": 220, "test_name": "Anti Nuclear Antibody (IF)", "test_code": "001045", "department": "SEROLOGY", "result_name": "Anti Nuclear Antibody (IF)", "parameter_name": "Anti Nuclear Antibody (IF)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IMMUNOFLORESN ASSAY", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.127519", "updated_at": "2025-07-08T19:27:21.127522"}, {"id": 221, "test_name": "ANTI PHOSPHOLIPID ANTIBODY IgA", "test_code": "001498", "department": "SEROLOGY", "result_name": "ANTI PHOSPHOLIPID ANTIBODY IgA", "parameter_name": "ANTI PHOSPHOLIPID ANTIBODY IgA", "unit": "U/mL", "result_type": null, "reference_range": "NEGATIVE : Less Than 12 EQUIVOCAL: 12-18    POSITIVE : More than 18", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "EIA", "specimen_type": "SERUM", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.127637", "updated_at": "2025-07-08T19:27:21.127640"}, {"id": 222, "test_name": "ANTI PHOSPHOLIPID ANTIBODY IgG", "test_code": "001077", "department": "SEROLOGY", "result_name": "ANTI PHOSPHOLIPID ANTIBODY IgG", "parameter_name": "ANTI PHOSPHOLIPID ANTIBODY IgG", "unit": "GPL.U/mL", "result_type": "Pick List", "reference_range": "Negative  : <10 Positive  : >=10", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELISA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.127739", "updated_at": "2025-07-08T19:27:21.127742"}, {"id": 223, "test_name": "ANTI PHOSPHOLIPID ANTIBODY IgM", "test_code": "001078", "department": "SEROLOGY", "result_name": "ANTI PHOSPHOLIPID ANTIBODY IgM", "parameter_name": "ANTI PHOSPHOLIPID ANTIBODY IgM", "unit": "MPL.U/ML", "result_type": "Pick List", "reference_range": "Negative  : <10 Positive  : >=10", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELISA", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.127838", "updated_at": "2025-07-08T19:27:21.127841"}, {"id": 224, "test_name": "ANTI SCL 70", "test_code": "001079", "department": "SEROLOGY", "result_name": "ANTI SCL 70", "parameter_name": "ANTI SCL 70", "unit": "Units", "result_type": null, "reference_range": "<20.00", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "EIA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.127939", "updated_at": "2025-07-08T19:27:21.127941"}, {"id": 225, "test_name": "ANTI SPERM ANTIBODY", "test_code": "001083", "department": "SEROLOGY", "result_name": "ANTI SPERM ANTIBODY", "parameter_name": "ANTI SPERM ANTIBODY", "unit": "U/mL", "result_type": null, "reference_range": "<60: NEGATIVE >60: POSITIVE", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "ELISA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.128037", "updated_at": "2025-07-08T19:27:21.128040"}, {"id": 226, "test_name": "Anti SS DNA", "test_code": "001057", "department": "SEROLOGY", "result_name": "Anti SS DNA", "parameter_name": "Anti SS DNA", "unit": "U/mL", "result_type": null, "reference_range": "<68.6      :  Negative   68.6-229   :  Moderate Positive >229       :  Strong Positive", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "ELISA", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.128138", "updated_at": "2025-07-08T19:27:21.128140"}, {"id": 227, "test_name": "Anti-HBc Total (Ab to Hep-B Core Ag)", "test_code": "001126", "department": "SEROLOGY", "result_name": "Anti-HBc Total (Ab to Hep-B Core Ag)", "parameter_name": "Anti-HBc Total (Ab to Hep-B Core Ag)", "unit": "index", "result_type": "Pick List", "reference_range": "Non Reactive: < 1 Reactive: >= 1", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ECLIA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.128239", "updated_at": "2025-07-08T19:27:21.128242"}, {"id": 228, "test_name": "Anti-La/SSB Antibody", "test_code": "001050", "department": "SEROLOGY", "result_name": "Anti-La/SSB Antibody", "parameter_name": "Anti-La/SSB Antibody", "unit": null, "result_type": "Pick List", "reference_range": "0  - 5  : <PERSON><PERSON><PERSON> 6  - 10 : Borderline 11 - 25 : Positive (+) 26 - 50 : Positive (++) >50     : Strong Positive (+++)", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.128336", "updated_at": "2025-07-08T19:27:21.128339"}, {"id": 229, "test_name": "Anti-SARS-CoV-2", "test_code": "001571", "department": "SEROLOGY", "result_name": "Anti-SARS-CoV-2", "parameter_name": "Anti-SARS-CoV-2", "unit": "COI", "result_type": "Pick List", "reference_range": "<1.0  : Nonreactive >=1.0 : Reactive", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ECLIA", "specimen_type": "SERUM", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.128436", "updated_at": "2025-07-08T19:27:21.128439"}, {"id": 230, "test_name": "Antibodies to Soluble Liver Antigen (SLA)", "test_code": "001286", "department": "SEROLOGY", "result_name": "Antibodies to Soluble Liver Antigen (SLA)", "parameter_name": "Antibodies to Soluble Liver Antigen (SLA)", "unit": "U/mL", "result_type": "Pick List", "reference_range": "Negative    :  < 12 Equivocal   :  12 - 18 Positive    :  > 18", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "E.I.A", "specimen_type": "SERUM", "container": "Serum Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.128537", "updated_at": "2025-07-08T19:27:21.128539"}, {"id": 231, "test_name": "Antibody to Sm Antigen", "test_code": "001217", "department": "SEROLOGY", "result_name": "Antibody to Sm Antigen", "parameter_name": "Antibody to Sm Antigen", "unit": null, "result_type": "Pick List", "reference_range": "0  - 5  : <PERSON><PERSON><PERSON> 6  - 10 : Borderline 11 - 25 : Positive (+) 26 - 50 : Positive (++) >50     : Strong Positive (+++)", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "BLOT", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.128636", "updated_at": "2025-07-08T19:27:21.128638"}, {"id": 232, "test_name": "ASCA - IgA", "test_code": "001224", "department": "SEROLOGY", "result_name": "ASCA - IgA", "parameter_name": "ASCA - IgA", "unit": "units", "result_type": "Pick List", "reference_range": "Negative  : 0.0 - 20.0  Borderline: 20.1 - 24.9 positive  : >= 25.0", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "E.I.A", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.128741", "updated_at": "2025-07-08T19:27:21.128743"}, {"id": 233, "test_name": "ASCA - IgG", "test_code": "001225", "department": "SEROLOGY", "result_name": "ASCA - IgG", "parameter_name": "ASCA - IgG", "unit": "units", "result_type": "Pick List", "reference_range": "Negative  : 0.0 - 20.0  Borderline: 20.1 - 24.9 positive  : >= 25.0", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "E.I.A", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.128839", "updated_at": "2025-07-08T19:27:21.128842"}, {"id": 234, "test_name": "ASO - Antistreptolysin O", "test_code": "001039", "department": "SEROLOGY", "result_name": "ASO - Antistreptolysin O", "parameter_name": "ASO - Antistreptolysin O", "unit": null, "result_type": null, "reference_range": "Less than 200 \nUpto 5 Yrs < 100", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "NEPHELOMETRY", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.128938", "updated_at": "2025-07-08T19:27:21.128941"}, {"id": 235, "test_name": "Aspergillosis Antibody - IgG", "test_code": "001088", "department": "SEROLOGY", "result_name": "Aspergillosis Antibody - IgG", "parameter_name": "Aspergillosis Antibody - IgG", "unit": null, "result_type": "Pick List", "reference_range": "Negative   : <8.0 Equivocal : 8.0 - 12.0 Positive  : > 12.0", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "E.I.A", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.129036", "updated_at": "2025-07-08T19:27:21.129039"}, {"id": 236, "test_name": "Aspergillosis Antibody - IgM", "test_code": "001089", "department": "SEROLOGY", "result_name": "Aspergillosis Antibody - IgM", "parameter_name": "Aspergillosis Antibody - IgM", "unit": "units", "result_type": "Pick List", "reference_range": "Negative  : <9.0 Equivocal : 9.0 - 11.0 Positive  : > 11.0", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "E.I.A", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.129135", "updated_at": "2025-07-08T19:27:21.129138"}, {"id": 237, "test_name": "Asper<PERSON><PERSON> fumigatus, Specific IgG", "test_code": "001618", "department": "SEROLOGY", "result_name": "Asper<PERSON><PERSON> fumigatus, Specific IgG", "parameter_name": "Asper<PERSON><PERSON> fumigatus, Specific IgG", "unit": "mgA/L", "result_type": null, "reference_range": "<=27.4", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Fluoro enzyme immunoassay", "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.129239", "updated_at": "2025-07-08T19:27:21.129242"}, {"id": 238, "test_name": "Bacterial Meningitis Panel", "test_code": "001368", "department": "SEROLOGY", "result_name": "Bacterial Meningitis Panel", "parameter_name": "Bacterial Meningitis Panel", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "Serum/CSF", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.129336", "updated_at": "2025-07-08T19:27:21.129339"}, {"id": 239, "test_name": "Beta-2-glycoprotein I-IgA", "test_code": "001506", "department": "SEROLOGY", "result_name": "Beta-2-glycoprotein I-IgA", "parameter_name": "Beta-2-glycoprotein I-IgA", "unit": "U/mL", "result_type": "Pick List", "reference_range": "Less than 5 : Negative 5 - 8       : Equivocal More than 8 : Positive", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "EIA", "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.129434", "updated_at": "2025-07-08T19:27:21.129437"}, {"id": 240, "test_name": "Beta-2-glycoprotein I-IgG", "test_code": "001281", "department": "SEROLOGY", "result_name": "Beta-2-glycoprotein I-IgG", "parameter_name": "Beta-2-glycoprotein I-IgG", "unit": "RU/mL", "result_type": "Pick List", "reference_range": "<20.0", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELISA", "specimen_type": "SERUM", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.129532", "updated_at": "2025-07-08T19:27:21.129535"}, {"id": 241, "test_name": "Beta-2-glycoprotein I-IgM", "test_code": "001282", "department": "SEROLOGY", "result_name": "Beta-2-glycoprotein I-IgM", "parameter_name": "Beta-2-glycoprotein I-IgM", "unit": "RU/mL", "result_type": "Pick List", "reference_range": "<20.0", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELISA", "specimen_type": "SERUM", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.129631", "updated_at": "2025-07-08T19:27:21.129634"}, {"id": 242, "test_name": "BK Virus Quantitative, Urine", "test_code": "001570", "department": "SEROLOGY", "result_name": "BK Virus Quantitative, Urine", "parameter_name": "BK Virus Quantitative, Urine", "unit": "Copies/mL", "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Real Time P.C.R", "specimen_type": "URINE", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.129731", "updated_at": "2025-07-08T19:27:21.129735"}, {"id": 243, "test_name": "<PERSON><PERSON><PERSON><PERSON> IgG", "test_code": "001627", "department": "SEROLOGY", "result_name": "<PERSON><PERSON><PERSON><PERSON> IgG", "parameter_name": "<PERSON><PERSON><PERSON><PERSON> IgG", "unit": "U/mL", "result_type": null, "reference_range": "Negative     : < 16 Indeterminate: 16-24 Positive     : > 24", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "EIA", "specimen_type": "SERUM", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.129832", "updated_at": "2025-07-08T19:27:21.129835"}, {"id": 244, "test_name": "Borrel<PERSON>(Lyme) - IgG Ab", "test_code": "001292", "department": "SEROLOGY", "result_name": "Borrel<PERSON>(Lyme) - IgG Ab", "parameter_name": "Borrel<PERSON>(Lyme) - IgG Ab", "unit": "AU/mL", "result_type": "Pick List", "reference_range": "Negative: < 10 Equivocal: 10-15 Positive: >= 15", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "CLIA", "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.129932", "updated_at": "2025-07-08T19:27:21.129934"}, {"id": 245, "test_name": "Borrel<PERSON>(Lyme)- IgM Ab", "test_code": "001293", "department": "SEROLOGY", "result_name": "Borrel<PERSON>(Lyme)- IgM Ab", "parameter_name": "Borrel<PERSON>(Lyme)- IgM Ab", "unit": "AU/mL", "result_type": "Pick List", "reference_range": "Negative: < 18                                                                                                             Equivocal: 18-22                                                                                                                 Positive: >= 22", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "CLIA", "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.130030", "updated_at": "2025-07-08T19:27:21.130032"}, {"id": 246, "test_name": "BRUCELLA  ANTIBODY - IGM", "test_code": "001090", "department": "SEROLOGY", "result_name": "BRUCELLA  ANTIBODY - IGM", "parameter_name": "BRUCELLA  ANTIBODY - IGM", "unit": "U/mL", "result_type": null, "reference_range": "Negative  : <8.0 Equivocal : 8.0 - 12.0 Positive  : >12.0", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "EIA", "specimen_type": "Serum", "container": "Serum Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.130128", "updated_at": "2025-07-08T19:27:21.130130"}, {"id": 247, "test_name": "BRUCELLA AGGLUTINATION TEST", "test_code": "001206", "department": "SEROLOGY", "result_name": "BRUCELLA AGGLUTINATION TEST", "parameter_name": "BRUCELLA AGGLUTINATION TEST", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.130224", "updated_at": "2025-07-08T19:27:21.130227"}, {"id": 248, "test_name": "BRUCELLA ANTIBODY - IGG", "test_code": "001092", "department": "SEROLOGY", "result_name": "BRUCELLA ANTIBODY - IGG", "parameter_name": "BRUCELLA ANTIBODY - IGG", "unit": "U/mL", "result_type": null, "reference_range": "Negative  : <8.0 Borderline: 8.0 - 12.0 Positive  : >12.0", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "EIA", "specimen_type": "Serum", "container": "Serum Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.130328", "updated_at": "2025-07-08T19:27:21.130330"}, {"id": 249, "test_name": "C-ANCA (PR3)", "test_code": "001038", "department": "SEROLOGY", "result_name": "C-ANCA (PR3)", "parameter_name": "C-ANCA (PR3)", "unit": "U/mL", "result_type": null, "reference_range": "Negative  : <12.0 Equivocal : 12.0 - 18.0 Positive  : >18.0", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "ELISA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.130427", "updated_at": "2025-07-08T19:27:21.130429"}, {"id": 250, "test_name": "Centromere Antibody IgG", "test_code": "001274", "department": "SEROLOGY", "result_name": "Centromere Antibody IgG", "parameter_name": "Centromere Antibody IgG", "unit": "units", "result_type": null, "reference_range": "0  - 5  : <PERSON><PERSON><PERSON> 6  - 10 : Borderline 11 - 25 : Positive (+) 26 - 50 : Positive (++) >50     : Strong Positive (+++)", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "E.I.A", "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.130525", "updated_at": "2025-07-08T19:27:21.130527"}, {"id": 251, "test_name": "CHIKUNGUNYA IgG (CARD)", "test_code": "001734", "department": "SEROLOGY", "result_name": "CHIKUNGUNYA IgG (CARD)", "parameter_name": "CHIKUNGUNYA IgG (CARD)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "SERUM", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.130621", "updated_at": "2025-07-08T19:27:21.130624"}, {"id": 252, "test_name": "CHIKUNGUNYA IgM (CARD)", "test_code": "001097", "department": "SEROLOGY", "result_name": "CHIKUNGUNYA IgM (CARD)", "parameter_name": "CHIKUNGUNYA IgM (CARD)", "unit": null, "result_type": "Pick List", "reference_range": "Negative", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IC", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.130719", "updated_at": "2025-07-08T19:27:21.130722"}, {"id": 253, "test_name": "CHIKUNGUNYA-IGM (ELISA)", "test_code": "001598", "department": "SEROLOGY", "result_name": "CHIKUNGUNYA-IGM (ELISA)", "parameter_name": "CHIKUNGUNYA-IGM (ELISA)", "unit": "index", "result_type": "Pick List", "reference_range": "Negative : < 0.90 Equivocal: 0.90 - 1.10 Positive : > 1.10", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELISA", "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.130822", "updated_at": "2025-07-08T19:27:21.130824"}, {"id": 254, "test_name": "CHLAMYDIAL ANTIBODY  IgM", "test_code": "001099", "department": "SEROLOGY", "result_name": "CHLAMYDIAL ANTIBODY  IgM", "parameter_name": "CHLAMYDIAL ANTIBODY  IgM", "unit": "<PERSON><PERSON>", "result_type": "Pick List", "reference_range": "<0.8       : Negative 0.8 - 1.1  : Equivocal >1.1       : Positive", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "EIA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.130920", "updated_at": "2025-07-08T19:27:21.130922"}, {"id": 255, "test_name": "CHLAMYDIAL ANTIBODY IgG", "test_code": "001100", "department": "SEROLOGY", "result_name": "CHLAMYDIAL ANTIBODY IgG", "parameter_name": "CHLAMYDIAL ANTIBODY IgG", "unit": "<PERSON><PERSON>", "result_type": "Pick List", "reference_range": "< 9       : Negative 9 - 11    : Equivocal >11       : <PERSON><PERSON>", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "EIA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.131018", "updated_at": "2025-07-08T19:27:21.131021"}, {"id": 256, "test_name": "Clostridium Difficile GDH", "test_code": "001575", "department": "SEROLOGY", "result_name": "Clostridium Difficile GDH", "parameter_name": "Clostridium Difficile GDH", "unit": "index", "result_type": null, "reference_range": "Negative  : <=0.9 Equivocal : 0.91-1.10 Positive  : >=1.10", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "CLIA", "specimen_type": null, "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.131114", "updated_at": "2025-07-08T19:27:21.131117"}, {"id": 257, "test_name": "Clostridium Difficile Toxin (A&B)", "test_code": "001294", "department": "SEROLOGY", "result_name": "Clostridium Difficile Toxin (A&B)", "parameter_name": "Clostridium Difficile Toxin (A&B)", "unit": "index", "result_type": "Pick List", "reference_range": "Negative   :  <=0.9 Equivocal  :  0.91 - 1.10 Positive    : >= 1.10", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "CLIA", "specimen_type": null, "container": "STOOL", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.131211", "updated_at": "2025-07-08T19:27:21.131213"}, {"id": 258, "test_name": "CMV - Avidity test", "test_code": "001272", "department": "SEROLOGY", "result_name": "CMV - Avidity test", "parameter_name": "CMV - Avidity test", "unit": "%", "result_type": "Pick List", "reference_range": "Low avidity           : <30  Grey zone mean avidity: 30 - 40 High avidity          : >40", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "E.I.A", "specimen_type": "SERUM", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.131314", "updated_at": "2025-07-08T19:27:21.131317"}, {"id": 259, "test_name": "CMV IgG", "test_code": "001101", "department": "SEROLOGY", "result_name": "CMV IgG", "parameter_name": "CMV IgG", "unit": "\nIU/mL\n", "result_type": null, "reference_range": "Nonreactive  : <0.5 Indeterminate: 0.5 - <1.0 Reactive     : >=1.0", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "ECLIA", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.131414", "updated_at": "2025-07-08T19:27:21.131417"}, {"id": 260, "test_name": "CMV IgM", "test_code": "001102", "department": "SEROLOGY", "result_name": "CMV IgM", "parameter_name": "CMV IgM", "unit": "COI", "result_type": null, "reference_range": "Nonreactive  : <0.70 Indeterminate: 0.70 - <1.00 Reactive     : >= 1.00", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "ECLIA", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.131514", "updated_at": "2025-07-08T19:27:21.131516"}, {"id": 261, "test_name": "COMPLETE ALLERGY PROFILE- VEG FOOD+ INHALANTS WITH CONTACTS + NON-VEG + DRUGS (222 Approx. Test)", "test_code": "001662", "department": "SEROLOGY", "result_name": "COMPLETE ALLERGY PROFILE- VEG FOOD+ INHALANTS WITH CONTACTS + NON-VEG + DRUGS (222 Approx. Test)", "parameter_name": "COMPLETE ALLERGY PROFILE- VEG FOOD+ INHALANTS WITH CONTACTS + NON-VEG + DRUGS (222 Approx. Test)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.131611", "updated_at": "2025-07-08T19:27:21.131614"}, {"id": 262, "test_name": "CRP-LATEX", "test_code": "001226", "department": "SEROLOGY", "result_name": "CRP-LATEX", "parameter_name": "CRP-LATEX", "unit": null, "result_type": "Pick List", "reference_range": "Negative", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "LATEX", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.131708", "updated_at": "2025-07-08T19:27:21.131711"}, {"id": 263, "test_name": "CRYPTOCOCCUS ANTIGEN", "test_code": "001104", "department": "SEROLOGY", "result_name": "CRYPTOCOCCUS ANTIGEN", "parameter_name": "CRYPTOCOCCUS ANTIGEN", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELISA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.131806", "updated_at": "2025-07-08T19:27:21.131809"}, {"id": 264, "test_name": "Cryptococcus Antigen, CSF", "test_code": "001248", "department": "SEROLOGY", "result_name": "Cryptococcus Antigen, CSF", "parameter_name": "Cryptococcus Antigen, CSF", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Latex Agglutination", "specimen_type": "CSF", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.131907", "updated_at": "2025-07-08T19:27:21.131910"}, {"id": 265, "test_name": "DENGUE Ab/Ag - Card", "test_code": "001253", "department": "SEROLOGY", "result_name": "DENGUE Ab/Ag - Card", "parameter_name": "DENGUE Ab/Ag - Card", "unit": null, "result_type": null, "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IC", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.132006", "updated_at": "2025-07-08T19:27:21.132009"}, {"id": 266, "test_name": "DENGUE ANTIBODY IgG", "test_code": "001114", "department": "SEROLOGY", "result_name": "DENGUE ANTIBODY IgG", "parameter_name": "DENGUE ANTIBODY IgG", "unit": "S/Co", "result_type": null, "reference_range": "Less than 1   : Non reactive\nAbove >=1     : Reactive", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "CLIA", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.132107", "updated_at": "2025-07-08T19:27:21.132109"}, {"id": 267, "test_name": "DENGUE ANTIBODY IgM", "test_code": "001115", "department": "SEROLOGY", "result_name": "DENGUE ANTIBODY IgM", "parameter_name": "DENGUE ANTIBODY IgM", "unit": "S/Co", "result_type": null, "reference_range": "Less than 1   : Non reactive\nAbove >=1     : Reactive", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "CLIA", "specimen_type": "Serum", "container": "\nPLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.132205", "updated_at": "2025-07-08T19:27:21.132207"}, {"id": 268, "test_name": "DENGUE IgG (CARD)", "test_code": "001247", "department": "SEROLOGY", "result_name": "DENGUE IgG (CARD)", "parameter_name": "DENGUE IgG (CARD)", "unit": null, "result_type": "Pick List", "reference_range": "NEGATIVE", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IC", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.132301", "updated_at": "2025-07-08T19:27:21.132304"}, {"id": 269, "test_name": "DENGUE IgM (CARD)", "test_code": "001246", "department": "SEROLOGY", "result_name": "DENGUE IgM (CARD)", "parameter_name": "DENGUE IgM (CARD)", "unit": null, "result_type": "Pick List", "reference_range": "NEGATIVE", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IC", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.132758", "updated_at": "2025-07-08T19:27:21.132764"}, {"id": 270, "test_name": "DENGUE Ns1 ANTIGEN - Card", "test_code": "001244", "department": "SEROLOGY", "result_name": "DENGUE Ns1 ANTIGEN - Card", "parameter_name": "DENGUE Ns1 ANTIGEN - Card", "unit": null, "result_type": "Pick List", "reference_range": "\nNEGATIVE", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IC", "specimen_type": null, "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.133055", "updated_at": "2025-07-08T19:27:21.133060"}, {"id": 271, "test_name": "DENGUE Ns1 ANTIGEN - CLIA", "test_code": "001279", "department": "SEROLOGY", "result_name": "DENGUE Ns1 ANTIGEN - CLIA", "parameter_name": "DENGUE Ns1 ANTIGEN - CLIA", "unit": "S/Co", "result_type": null, "reference_range": "Less than 1    :  Non reactive\nAbove >=  1     : Reactive", "critical_low": 1.0, "critical_high": 1.0, "decimal_places": 0, "method": "CLIA", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.133307", "updated_at": "2025-07-08T19:27:21.133311"}, {"id": 272, "test_name": "DENGUE PROFILE", "test_code": "000000", "department": "SEROLOGY", "result_name": "DENGUE PROFILE", "parameter_name": "DENGUE PROFILE", "unit": null, "result_type": null, "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.133531", "updated_at": "2025-07-08T19:27:21.133535"}, {"id": 273, "test_name": "DIPHTHERIA IgG-Antibodies", "test_code": "001216", "department": "SEROLOGY", "result_name": "DIPHTHERIA IgG-Antibodies", "parameter_name": "DIPHTHERIA IgG-Antibodies", "unit": "IU/ml", "result_type": null, "reference_range": "< 0.01     - No Protection  0.01 - 0.1 - Minimal Protection  0.10 - 1.0 - Safe Protection  > 1.00     - Long Term Protection", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.133715", "updated_at": "2025-07-08T19:27:21.133718"}, {"id": 274, "test_name": "DPT Antibodies (Diphtheria,Pertussis,Tetanus)-IgG", "test_code": "001517", "department": "SEROLOGY", "result_name": "DPT Antibodies (Diphtheria,Pertussis,Tetanus)-IgG", "parameter_name": "DPT Antibodies (Diphtheria,Pertussis,Tetanus)-IgG", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "EIA", "specimen_type": "SERUM", "container": "Serum Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.133830", "updated_at": "2025-07-08T19:27:21.133833"}, {"id": 275, "test_name": "EBV  - MONOSPOT", "test_code": "001266", "department": "SEROLOGY", "result_name": "EBV  - MONOSPOT", "parameter_name": "EBV  - MONOSPOT", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Agglutination", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.133939", "updated_at": "2025-07-08T19:27:21.133942"}, {"id": 276, "test_name": "EBV (NA) IgG", "test_code": "001261", "department": "SEROLOGY", "result_name": "EBV (NA) IgG", "parameter_name": "EBV (NA) IgG", "unit": "U/mL", "result_type": "Pick List", "reference_range": "Negative: < 5 Equivocal: 5-20 Positive: >= 20", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "CLIA", "specimen_type": "Serum", "container": "Serum Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.134048", "updated_at": "2025-07-08T19:27:21.134051"}, {"id": 277, "test_name": "EBV (NA) IgM", "test_code": "001262", "department": "SEROLOGY", "result_name": "EBV (NA) IgM", "parameter_name": "EBV (NA) IgM", "unit": "U/mL", "result_type": "Pick List", "reference_range": "Negative  : <8.0 Equivocal : 8.0 - 12.0 Positive  : >12.0", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "EIA", "specimen_type": "Serum", "container": "Serum Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.134178", "updated_at": "2025-07-08T19:27:21.134181"}, {"id": 278, "test_name": "EBV (VCA) IgG", "test_code": "001263", "department": "SEROLOGY", "result_name": "EBV (VCA) IgG", "parameter_name": "EBV (VCA) IgG", "unit": "U/mL", "result_type": "Pick List", "reference_range": "Negative: < 20   Positive: >= 20", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "CLIA", "specimen_type": "SERUM", "container": "Serum Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.134299", "updated_at": "2025-07-08T19:27:21.134302"}, {"id": 279, "test_name": "EBV (VCA) IgM", "test_code": "001264", "department": "SEROLOGY", "result_name": "EBV (VCA) IgM", "parameter_name": "EBV (VCA) IgM", "unit": "U/mL", "result_type": "Pick List", "reference_range": "NEGATIVE   :  < 20 EQUIVOCAL  :  20 - 39 POSITIVE   :  >= 40", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "CLIA", "specimen_type": "SERUM", "container": "Serum Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.134403", "updated_at": "2025-07-08T19:27:21.134406"}, {"id": 280, "test_name": "ECHINOCOCCUS (HYDATID CYST) DETECTION", "test_code": "001577", "department": "SEROLOGY", "result_name": "ECHINOCOCCUS (HYDATID CYST) DETECTION", "parameter_name": "ECHINOCOCCUS (HYDATID CYST) DETECTION", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "FLUID", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.134502", "updated_at": "2025-07-08T19:27:21.134505"}, {"id": 281, "test_name": "Echinococcus(Hydatid Cyst) IgG", "test_code": "001243", "department": "SEROLOGY", "result_name": "Echinococcus(Hydatid Cyst) IgG", "parameter_name": "Echinococcus(Hydatid Cyst) IgG", "unit": "NTU", "result_type": "Numeric", "reference_range": "Negative: < 9.0 Positive: > 11 Equivocal: 9.0-11", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "EIA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.134603", "updated_at": "2025-07-08T19:27:21.134606"}, {"id": 282, "test_name": "ENA PROFILE", "test_code": "000000", "department": "SEROLOGY", "result_name": "ENA PROFILE", "parameter_name": "ENA PROFILE", "unit": null, "result_type": null, "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.134724", "updated_at": "2025-07-08T19:27:21.134727"}, {"id": 283, "test_name": "ENDOMYCIAL ANTIBODY IGG", "test_code": "001509", "department": "SEROLOGY", "result_name": "ENDOMYCIAL ANTIBODY IGG", "parameter_name": "ENDOMYCIAL ANTIBODY IGG", "unit": null, "result_type": "Pick list", "reference_range": "Negative  Sample screening dilution is 1:10", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Immunofluorescence", "specimen_type": "SERUM", "container": "Serum Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.134842", "updated_at": "2025-07-08T19:27:21.134845"}, {"id": 284, "test_name": "Endomysial Antibody - IgA", "test_code": "001245", "department": "SEROLOGY", "result_name": "Endomysial Antibody - IgA", "parameter_name": "Endomysial Antibody - IgA", "unit": null, "result_type": "Pick List", "reference_range": "NEGATIVE  ", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IFA", "specimen_type": "Serum", "container": "Serum Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.134944", "updated_at": "2025-07-08T19:27:21.134947"}, {"id": 285, "test_name": "Febrile Agglutination Test", "test_code": "001221", "department": "SEROLOGY", "result_name": "Febrile Agglutination Test", "parameter_name": "Febrile Agglutination Test", "unit": null, "result_type": "Numeric", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.135042", "updated_at": "2025-07-08T19:27:21.135045"}, {"id": 286, "test_name": "FTA-Abs IgG", "test_code": "001118", "department": "SEROLOGY", "result_name": "FTA-Abs IgG", "parameter_name": "FTA-Abs IgG", "unit": null, "result_type": "Pick List", "reference_range": "Negative", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "1:10 by Immunofluorescence", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.135140", "updated_at": "2025-07-08T19:27:21.135143"}, {"id": 287, "test_name": "FTA-Abs IgM", "test_code": "001234", "department": "SEROLOGY", "result_name": "FTA-Abs IgM", "parameter_name": "FTA-Abs IgM", "unit": null, "result_type": "Pick List", "reference_range": "Negative", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "1:10 by Immunofluorescence", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.135240", "updated_at": "2025-07-08T19:27:21.135261"}, {"id": 288, "test_name": "Ganglioside antibody IgG (Anti GM1)", "test_code": "001284", "department": "SEROLOGY", "result_name": "Ganglioside antibody IgG (Anti GM1)", "parameter_name": "Ganglioside antibody IgG (Anti GM1)", "unit": null, "result_type": "Template", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Methyl Re<PERSON>ufin", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.135401", "updated_at": "2025-07-08T19:27:21.135404"}, {"id": 289, "test_name": "GBM - Glomerular Basement Membrane Antibody", "test_code": "001238", "department": "SEROLOGY", "result_name": "GBM - Glomerular Basement Membrane Antibody", "parameter_name": "GBM - Glomerular Basement Membrane Antibody", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "Serum", "container": "Serum Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.135659", "updated_at": "2025-07-08T19:27:21.135664"}, {"id": 290, "test_name": "GERMAN MEASLES (RUBELLA IGG)", "test_code": "001120", "department": "SEROLOGY", "result_name": "GERMAN MEASLES (RUBELLA IGG)", "parameter_name": "GERMAN MEASLES (RUBELLA IGG)", "unit": null, "result_type": "Pick List", "reference_range": "Negative: <4.9 Equivocal: 5.0 - 9.9 Positive : >=10", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "CMIA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.135804", "updated_at": "2025-07-08T19:27:21.135808"}, {"id": 291, "test_name": "<PERSON><PERSON>, Stool", "test_code": "001554", "department": "SEROLOGY", "result_name": "<PERSON><PERSON>, Stool", "parameter_name": "<PERSON><PERSON>, Stool", "unit": null, "result_type": "Pick List", "reference_range": "Negative", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Immuno Chromatography", "specimen_type": "STOOL", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.135993", "updated_at": "2025-07-08T19:27:21.135996"}, {"id": 292, "test_name": "HAMS TEST", "test_code": "001242", "department": "SEROLOGY", "result_name": "HAMS TEST", "parameter_name": "HAMS TEST", "unit": null, "result_type": "Pick List", "reference_range": "NEGATIVE", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Hemolysis", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.136110", "updated_at": "2025-07-08T19:27:21.136113"}, {"id": 293, "test_name": "HAV IGM-Antibody", "test_code": "001125", "department": "SEROLOGY", "result_name": "HAV IGM-Antibody", "parameter_name": "HAV IGM-Antibody", "unit": null, "result_type": null, "reference_range": "< 0.8     - Non-Reactive  0.8 - 1.2 - Gray zone  > 1.2     - Reactive", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELISA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.136214", "updated_at": "2025-07-08T19:27:21.136216"}, {"id": 294, "test_name": "Hbe Ag", "test_code": "001127", "department": "SEROLOGY", "result_name": "Hbe Ag", "parameter_name": "Hbe Ag", "unit": "S/Co", "result_type": "Pick List", "reference_range": "NON REACTIVE :< 1.00 REACTIVE     :>=1.00", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "CMIA", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.136318", "updated_at": "2025-07-08T19:27:21.136321"}, {"id": 295, "test_name": "HBsAg", "test_code": "001129", "department": "SEROLOGY", "result_name": "HBsAg", "parameter_name": "HBsAg", "unit": "S/Co", "result_type": "Pick List", "reference_range": "<1.0 - Non Reactive >=1.0- Reactive", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.136424", "updated_at": "2025-07-08T19:27:21.136427"}, {"id": 296, "test_name": "HBsAg (CARD)", "test_code": "001128", "department": "SEROLOGY", "result_name": "HBsAg (CARD)", "parameter_name": "HBsAg (CARD)", "unit": null, "result_type": "Pick List", "reference_range": "NEGATIVE", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IC", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.136758", "updated_at": "2025-07-08T19:27:21.136761"}, {"id": 297, "test_name": "HBsAg (ELISA)", "test_code": "001280", "department": "SEROLOGY", "result_name": "HBsAg (ELISA)", "parameter_name": "HBsAg (ELISA)", "unit": "OD Ratio", "result_type": "Pick List", "reference_range": "Negative : < Positive : >=", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELISA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.136987", "updated_at": "2025-07-08T19:27:21.136991"}, {"id": 298, "test_name": "HBsAg Confirmation", "test_code": "001130", "department": "SEROLOGY", "result_name": "HBsAg Confirmation", "parameter_name": "HBsAg Confirmation", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "Serum", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.137296", "updated_at": "2025-07-08T19:27:21.137300"}, {"id": 299, "test_name": "HBsAg- CLIA", "test_code": "001649", "department": "SEROLOGY", "result_name": "HBsAg- CLIA", "parameter_name": "HBsAg- CLIA", "unit": "S/Co", "result_type": null, "reference_range": "Non reactive: < 1.0\nReactive : >= 1.0", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "CLIA", "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.137482", "updated_at": "2025-07-08T19:27:21.137486"}, {"id": 300, "test_name": "Helicobacter Pylori -IgA antibodies", "test_code": "001516", "department": "SEROLOGY", "result_name": "Helicobacter Pylori -IgA antibodies", "parameter_name": "Helicobacter Pylori -IgA antibodies", "unit": "<PERSON><PERSON>", "result_type": null, "reference_range": "Negative   : <0.8 Equivocal  : 0.8-1.1 Positive   : >1.1", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "EIA", "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.137656", "updated_at": "2025-07-08T19:27:21.137659"}, {"id": 301, "test_name": "Helicobacter Pylori -IgM antibodies", "test_code": "001121", "department": "SEROLOGY", "result_name": "Helicobacter Pylori -IgM antibodies", "parameter_name": "Helicobacter Pylori -IgM antibodies", "unit": "U/mL", "result_type": null, "reference_range": "NEGATIVE : < 8.00 EQUIVOCAL: 8 - 12 POSITIVE : > 12.00", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "EIA", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.137863", "updated_at": "2025-07-08T19:27:21.137866"}, {"id": 302, "test_name": "Helicobacter Pylori-IgG antibodies", "test_code": "001122", "department": "SEROLOGY", "result_name": "Helicobacter Pylori-IgG antibodies", "parameter_name": "Helicobacter Pylori-IgG antibodies", "unit": "\nU/mL\n", "result_type": null, "reference_range": "Negative      : < 0.9 Indeterminate : 0.9 - 1.1 Positive      : > 1.1 & Above", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "CLIA", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.137970", "updated_at": "2025-07-08T19:27:21.137973"}, {"id": 303, "test_name": "HISTONE ANTIBODY.", "test_code": "001232", "department": "SEROLOGY", "result_name": "HISTONE ANTIBODY.", "parameter_name": "HISTONE ANTIBODY.", "unit": "U/mL", "result_type": null, "reference_range": "0  - 5  : <PERSON><PERSON><PERSON> 6  - 10 : Borderline 11 - 25 : Positive (+) 26 - 50 : Positive (++) >50     : Strong Positive(+++)", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELISA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.138073", "updated_at": "2025-07-08T19:27:21.138076"}, {"id": 304, "test_name": "Histoplasma Antibody", "test_code": "001297", "department": "SEROLOGY", "result_name": "Histoplasma Antibody", "parameter_name": "Histoplasma Antibody", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IMMUNODIFFUSION", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.138175", "updated_at": "2025-07-08T19:27:21.138178"}, {"id": 305, "test_name": "HIV I & 2 (CLIA)", "test_code": "001670", "department": "SEROLOGY", "result_name": "HIV I & 2 (CLIA)", "parameter_name": "HIV I & 2 (CLIA)", "unit": null, "result_type": "Pick List", "reference_range": "<1.0 Negative >1.0 Positive S/Co", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "CLIA", "specimen_type": "SERUM", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.138277", "updated_at": "2025-07-08T19:27:21.138280"}, {"id": 306, "test_name": "HIV p24 ANTIGEN", "test_code": "001072", "department": "SEROLOGY", "result_name": "HIV p24 ANTIGEN", "parameter_name": "HIV p24 ANTIGEN", "unit": null, "result_type": "Pick List", "reference_range": "\nNEGATIVE\n", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELFA", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.138502", "updated_at": "2025-07-08T19:27:21.138505"}, {"id": 307, "test_name": "HSV  I (IgG)", "test_code": "001135", "department": "SEROLOGY", "result_name": "HSV  I (IgG)", "parameter_name": "HSV  I (IgG)", "unit": "OD Ratio", "result_type": null, "reference_range": "NEGATIVE : < 0.80 EQUIVOCAL: 0.80 - 1.20 POSITIVE : > 1.20", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "ELISA", "specimen_type": "Serum", "container": "Serum Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.138617", "updated_at": "2025-07-08T19:27:21.138619"}, {"id": 308, "test_name": "HSV 1 & 2 IgG", "test_code": "001136", "department": "SEROLOGY", "result_name": "HSV 1 & 2 IgG", "parameter_name": "HSV 1 & 2 IgG", "unit": "OD Ratio", "result_type": null, "reference_range": "< 0.8       : Negative > 0.8-<1.1  : Borderline > 1.1       : Positive", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "E.L.I.S.A", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.138716", "updated_at": "2025-07-08T19:27:21.138719"}, {"id": 309, "test_name": "HSV 1 & 2 IgM", "test_code": "001137", "department": "SEROLOGY", "result_name": "HSV 1 & 2 IgM", "parameter_name": "HSV 1 & 2 IgM", "unit": "OD Ratio", "result_type": null, "reference_range": "< 0.8       : Negative > 0.8-<1.1  : Borderline > 1.1       : Positive", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "E.L.I.S.A", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.138836", "updated_at": "2025-07-08T19:27:21.138838"}, {"id": 310, "test_name": "HSV I (IgM)", "test_code": "001140", "department": "SEROLOGY", "result_name": "HSV I (IgM)", "parameter_name": "HSV I (IgM)", "unit": "OD Ratio", "result_type": null, "reference_range": "NEGATIVE : < 0.80 EQUIVOCAL: 0.80 - 1.20 POSITIVE : > 1.20", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "ELISA", "specimen_type": "Serum", "container": "Serum Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.139092", "updated_at": "2025-07-08T19:27:21.139097"}, {"id": 311, "test_name": "HSV II (IgG)", "test_code": "001138", "department": "SEROLOGY", "result_name": "HSV II (IgG)", "parameter_name": "HSV II (IgG)", "unit": "OD Ratio", "result_type": null, "reference_range": "NEGATIVE : < 0.80 EQUIVOCAL: 0.80 - 1.20 POSITIVE : > 1.20", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "ELISA", "specimen_type": "Serum", "container": "Serum Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.140133", "updated_at": "2025-07-08T19:27:21.140144"}, {"id": 312, "test_name": "HSV II (IgM)", "test_code": "001251", "department": "SEROLOGY", "result_name": "HSV II (IgM)", "parameter_name": "HSV II (IgM)", "unit": "OD Ratio", "result_type": null, "reference_range": "NEGATIVE : < 0.80 EQUIVOCAL: 0.80 - 1.20 POSITIVE : > 1.20", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "ELISA", "specimen_type": "Serum", "container": "Serum Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.140456", "updated_at": "2025-07-08T19:27:21.140461"}, {"id": 313, "test_name": "HTLV (I & II) Antibody", "test_code": "001295", "department": "SEROLOGY", "result_name": "HTLV (I & II) Antibody", "parameter_name": "HTLV (I & II) Antibody", "unit": "S/Co", "result_type": "Pick List", "reference_range": "<1.0 : Non Reactive >=1.0: Reactive", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "EIA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.140596", "updated_at": "2025-07-08T19:27:21.140599"}, {"id": 314, "test_name": "Influenza A & B", "test_code": "001229", "department": "SEROLOGY", "result_name": "Influenza A & B", "parameter_name": "Influenza A & B", "unit": null, "result_type": null, "reference_range": "Negative     (Screening test)", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IC", "specimen_type": "<PERSON><PERSON>", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.140817", "updated_at": "2025-07-08T19:27:21.140820"}, {"id": 315, "test_name": "Interferon Gamma Release Assay - TB", "test_code": "001191", "department": "SEROLOGY", "result_name": "Interferon Gamma Release Assay - TB", "parameter_name": "Interferon Gamma Release Assay - TB", "unit": "IU/mL", "result_type": "Pick List", "reference_range": "Positive : Above 0.35", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELISA", "specimen_type": "BLOOD", "container": null, "instructions": "Sample collection Container to be collected from Lab", "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.140960", "updated_at": "2025-07-08T19:27:21.140963"}, {"id": 316, "test_name": "Intrinsic factor antibody", "test_code": "001291", "department": "SEROLOGY", "result_name": "Intrinsic factor antibody", "parameter_name": "Intrinsic factor antibody", "unit": null, "result_type": "Pick List", "reference_range": "Negative (1:10 dil)", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Immunofluorescence", "specimen_type": "SERUM", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.141112", "updated_at": "2025-07-08T19:27:21.141133"}, {"id": 317, "test_name": "Jo-1 Antibody", "test_code": "001219", "department": "SEROLOGY", "result_name": "Jo-1 Antibody", "parameter_name": "Jo-1 Antibody", "unit": null, "result_type": "Pick List", "reference_range": "0  - 5  : <PERSON><PERSON><PERSON> 6  - 10 : Borderline 11 - 25 : Positive (+) 26 - 50 : Positive (++) >50     : Strong Positive (+++)", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "BLOT", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.141375", "updated_at": "2025-07-08T19:27:21.141379"}, {"id": 318, "test_name": "Legionella Pneumophila antigen", "test_code": "001628", "department": "SEROLOGY", "result_name": "Legionella Pneumophila antigen", "parameter_name": "Legionella Pneumophila antigen", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Immunochromatography", "specimen_type": "URINE", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.141493", "updated_at": "2025-07-08T19:27:21.141496"}, {"id": 319, "test_name": "LEPTOSPIRA By (DFM)", "test_code": "001249", "department": "SEROLOGY", "result_name": "LEPTOSPIRA By (DFM)", "parameter_name": "LEPTOSPIRA By (DFM)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "DFM", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.141634", "updated_at": "2025-07-08T19:27:21.141638"}, {"id": 320, "test_name": "LEPTOSPIRA BY MAT TEST", "test_code": "001236", "department": "SEROLOGY", "result_name": "LEPTOSPIRA BY MAT TEST", "parameter_name": "LEPTOSPIRA BY MAT TEST", "unit": null, "result_type": "Pick List", "reference_range": "NEGATIVE", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Agglutination", "specimen_type": "SERUM", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.141748", "updated_at": "2025-07-08T19:27:21.141751"}, {"id": 321, "test_name": "LEPTOSPIRA IgG", "test_code": "001231", "department": "SEROLOGY", "result_name": "LEPTOSPIRA IgG", "parameter_name": "LEPTOSPIRA IgG", "unit": "NTU", "result_type": "Pick List", "reference_range": "Negative  : < 9  Equivocal : 9 - 11  Positive  : > 11", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELISA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.141907", "updated_at": "2025-07-08T19:27:21.141930"}, {"id": 322, "test_name": "LEPTOSPIRA IgM", "test_code": "001207", "department": "SEROLOGY", "result_name": "LEPTOSPIRA IgM", "parameter_name": "LEPTOSPIRA IgM", "unit": "COI", "result_type": "Pick List", "reference_range": "< 0.9 NEGATIVE 0.9-1.1 EQUIVOCAL >1.1 POSITIVE", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELISA", "specimen_type": "Serum", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.142062", "updated_at": "2025-07-08T19:27:21.142065"}, {"id": 323, "test_name": "LEPTOSPIRAL IgG & IgM (Card)", "test_code": "001269", "department": "SEROLOGY", "result_name": "LEPTOSPIRAL IgG & IgM (Card)", "parameter_name": "LEPTOSPIRAL IgG & IgM (Card)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IC", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.142161", "updated_at": "2025-07-08T19:27:21.142164"}, {"id": 324, "test_name": "LKM1-Antibodies", "test_code": "001237", "department": "SEROLOGY", "result_name": "LKM1-Antibodies", "parameter_name": "LKM1-Antibodies", "unit": null, "result_type": "Pick List", "reference_range": "NEGATIVE (1:40)", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IFA", "specimen_type": "Serum", "container": "Serum Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.142261", "updated_at": "2025-07-08T19:27:21.142263"}, {"id": 325, "test_name": "Lyme Borrelia <PERSON>dorferi IgM Antibodies", "test_code": "001335", "department": "SEROLOGY", "result_name": "Lyme Borrelia <PERSON>dorferi IgM Antibodies", "parameter_name": "Lyme Borrelia <PERSON>dorferi IgM Antibodies", "unit": "AU/mL", "result_type": "Pick List", "reference_range": "\nNegative : < 18 Equivocal: 18-21 Positive : >=22\n", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "CLIA", "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.142360", "updated_at": "2025-07-08T19:27:21.142363"}, {"id": 326, "test_name": "LYMES ANTIBODY", "test_code": "001145", "department": "SEROLOGY", "result_name": "LYMES ANTIBODY", "parameter_name": "LYMES ANTIBODY", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELISA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.142464", "updated_at": "2025-07-08T19:27:21.142468"}, {"id": 327, "test_name": "MEASLES (Rubeola) - ANTIBODY IgM", "test_code": "001147", "department": "SEROLOGY", "result_name": "MEASLES (Rubeola) - ANTIBODY IgM", "parameter_name": "MEASLES (Rubeola) - ANTIBODY IgM", "unit": "index", "result_type": "Pick List", "reference_range": "NEGATIVE   : <0.90 EQUIVOCAL  : 0.90 - 1.10 POSITIVE   : >1.10", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "EIA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.142639", "updated_at": "2025-07-08T19:27:21.142641"}, {"id": 328, "test_name": "MEASLES (Rubeola) IgG", "test_code": "001170", "department": "SEROLOGY", "result_name": "MEASLES (Rubeola) IgG", "parameter_name": "MEASLES (Rubeola) IgG", "unit": null, "result_type": null, "reference_range": "Negative  : <13.5  Equivocal : 13.5-16.5 Positive  : >=16.5", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "CLIA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.142799", "updated_at": "2025-07-08T19:27:21.142801"}, {"id": 329, "test_name": "<PERSON><PERSON><PERSON>, Serum", "test_code": "001146", "department": "SEROLOGY", "result_name": "<PERSON><PERSON><PERSON>, Serum", "parameter_name": "<PERSON><PERSON><PERSON>, Serum", "unit": "U/mL", "result_type": null, "reference_range": "Refer Inerpretation", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "EIA", "specimen_type": "SERUM", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.142898", "updated_at": "2025-07-08T19:27:21.142900"}, {"id": 330, "test_name": "MITOCHONDRIAL ANTIBODIES (AMA)", "test_code": "001076", "department": "SEROLOGY", "result_name": "MITOCHONDRIAL ANTIBODIES (AMA)", "parameter_name": "MITOCHONDRIAL ANTIBODIES (AMA)", "unit": null, "result_type": "Pick List", "reference_range": "NEGATIVE (1:40)", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IFA", "specimen_type": "Serum", "container": "Serum Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.142996", "updated_at": "2025-07-08T19:27:21.142998"}, {"id": 331, "test_name": "Mumps IgG", "test_code": "001148", "department": "SEROLOGY", "result_name": "Mumps IgG", "parameter_name": "Mumps IgG", "unit": null, "result_type": null, "reference_range": "NEGATIVE    : <9 EQUIVOCAL   : 9 - 11 POSITIVE    : >11", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "CLIA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.143114", "updated_at": "2025-07-08T19:27:21.143117"}, {"id": 332, "test_name": "MUMPS IgM", "test_code": "001149", "department": "SEROLOGY", "result_name": "MUMPS IgM", "parameter_name": "MUMPS IgM", "unit": "index", "result_type": "Pick List", "reference_range": "Negative   : <0.9 Borderline : >0.9 - <1.1 Positive   : >=1.1", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "CLIA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.143228", "updated_at": "2025-07-08T19:27:21.143231"}, {"id": 333, "test_name": "MUSK <PERSON>, Myasthenia gravis", "test_code": "001296", "department": "SEROLOGY", "result_name": "MUSK <PERSON>, Myasthenia gravis", "parameter_name": "MUSK <PERSON>, Myasthenia gravis", "unit": "U/mL", "result_type": "Pick List", "reference_range": "Negative : < 0.4 Positive :  > 0.4", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "EIA", "specimen_type": "Serum", "container": "Serum Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.143331", "updated_at": "2025-07-08T19:27:21.143333"}, {"id": 334, "test_name": "Mycoplasma pneumoniae - IgG", "test_code": "001277", "department": "SEROLOGY", "result_name": "Mycoplasma pneumoniae - IgG", "parameter_name": "Mycoplasma pneumoniae - IgG", "unit": "NTU", "result_type": "Pick List", "reference_range": "Negative      : < 9  Indeterminate : 9-11 Positive      : > 11", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "EIA", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.143426", "updated_at": "2025-07-08T19:27:21.143429"}, {"id": 335, "test_name": "Mycoplasma pneumoniae - IgM", "test_code": "001151", "department": "SEROLOGY", "result_name": "Mycoplasma pneumoniae - IgM", "parameter_name": "Mycoplasma pneumoniae - IgM", "unit": "index", "result_type": null, "reference_range": "Negative : <10.0 Positive : >10.0", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "CLIA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.143523", "updated_at": "2025-07-08T19:27:21.143526"}, {"id": 336, "test_name": "Myositis Profile", "test_code": "001519", "department": "SEROLOGY", "result_name": "Myositis Profile", "parameter_name": "Myositis Profile", "unit": null, "result_type": "Template", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "BLOT", "specimen_type": "Serum/CSF", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.143619", "updated_at": "2025-07-08T19:27:21.143622"}, {"id": 337, "test_name": "Neuronal Antibody Profile", "test_code": "001325", "department": "SEROLOGY", "result_name": "Neuronal Antibody Profile", "parameter_name": "Neuronal Antibody Profile", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Methyl Re<PERSON>ufin", "specimen_type": "SERUM", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.143753", "updated_at": "2025-07-08T19:27:21.143756"}, {"id": 338, "test_name": "NMDA Receptor Antibody (NR1)", "test_code": "001351", "department": "SEROLOGY", "result_name": "NMDA Receptor Antibody (NR1)", "parameter_name": "NMDA Receptor Antibody (NR1)", "unit": null, "result_type": "Pick List", "reference_range": "Negative", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Immunofluorescence", "specimen_type": "Serum/CSF", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.143850", "updated_at": "2025-07-08T19:27:21.143853"}, {"id": 339, "test_name": "Ovarian Antibody (AOA)", "test_code": "001255", "department": "SEROLOGY", "result_name": "Ovarian Antibody (AOA)", "parameter_name": "Ovarian Antibody (AOA)", "unit": null, "result_type": "Pick List", "reference_range": "Negative", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Immunofluorescence (1:10)", "specimen_type": "Serum", "container": "Serum Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.143948", "updated_at": "2025-07-08T19:27:21.143951"}, {"id": 340, "test_name": "P C R  - Hepatitis B", "test_code": "001153", "department": "SEROLOGY", "result_name": "P C R  - Hepatitis B", "parameter_name": "P C R  - Hepatitis B", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "P.C.R", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.144045", "updated_at": "2025-07-08T19:27:21.144048"}, {"id": 341, "test_name": "P-ANCA (MPO)", "test_code": "001037", "department": "SEROLOGY", "result_name": "P-ANCA (MPO)", "parameter_name": "P-ANCA (MPO)", "unit": "U/mL", "result_type": null, "reference_range": "Negative  : <12.0 Equivocal : 12.0 - 18.0 Positive  : >18.0", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "ELISA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.144143", "updated_at": "2025-07-08T19:27:21.144146"}, {"id": 342, "test_name": "Parietal Cell Antibody (GPCA)", "test_code": "001275", "department": "SEROLOGY", "result_name": "Parietal Cell Antibody (GPCA)", "parameter_name": "Parietal Cell Antibody (GPCA)", "unit": null, "result_type": "Pick List", "reference_range": "Negative (1:10 dil)", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Immunofluorescence", "specimen_type": "Serum", "container": "Serum Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.144263", "updated_at": "2025-07-08T19:27:21.144266"}, {"id": 343, "test_name": "Parvovirus  (B19) IgG", "test_code": "001259", "department": "SEROLOGY", "result_name": "Parvovirus  (B19) IgG", "parameter_name": "Parvovirus  (B19) IgG", "unit": "index", "result_type": "Pick List", "reference_range": "Negative    : < 0.9 Borderline  : 0.9-1.1 Positive    : >= 1.1", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "E.I.A", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.144374", "updated_at": "2025-07-08T19:27:21.144377"}, {"id": 344, "test_name": "Parvovirus (B19) IgM", "test_code": "001260", "department": "SEROLOGY", "result_name": "Parvovirus (B19) IgM", "parameter_name": "Parvovirus (B19) IgM", "unit": "index", "result_type": "Pick List", "reference_range": "Negative: < 0.9 Borderline: 0.9-1.1 Positive: >= 1.1", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "CLIA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.144470", "updated_at": "2025-07-08T19:27:21.144473"}, {"id": 345, "test_name": "PAUL BUNNEL TEST (IM - SCREENING)", "test_code": "001154", "department": "SEROLOGY", "result_name": "PAUL BUNNEL TEST (IM - SCREENING)", "parameter_name": "PAUL BUNNEL TEST (IM - SCREENING)", "unit": null, "result_type": "Pick List", "reference_range": "No agglutination", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Agglutination", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.144567", "updated_at": "2025-07-08T19:27:21.144570"}, {"id": 346, "test_name": "RA (ELISA)", "test_code": "001158", "department": "SEROLOGY", "result_name": "RA (ELISA)", "parameter_name": "RA (ELISA)", "unit": "IU/mI", "result_type": null, "reference_range": "<15 IU/ml INTERPRETATION : INCREASED IN RHEUMATOID ARTHRITIS", "critical_low": 0.01, "critical_high": 11.0, "decimal_places": 2, "method": "ELISA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.144665", "updated_at": "2025-07-08T19:27:21.144667"}, {"id": 347, "test_name": "RA - FACTOR", "test_code": "001162", "department": "SEROLOGY", "result_name": "RA - FACTOR", "parameter_name": "RA - FACTOR", "unit": "IU/mI", "result_type": null, "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "NEPHELOMETRY", "specimen_type": "Serum", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.144762", "updated_at": "2025-07-08T19:27:21.144783"}, {"id": 348, "test_name": "RA FACTOR-LATEX", "test_code": "001159", "department": "SEROLOGY", "result_name": "RA FACTOR-LATEX", "parameter_name": "RA FACTOR-LATEX", "unit": null, "result_type": "Pick List", "reference_range": "NEGATIVE", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "LATEX", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.144895", "updated_at": "2025-07-08T19:27:21.144898"}, {"id": 349, "test_name": "Rabies antibody", "test_code": "001213", "department": "SEROLOGY", "result_name": "Rabies antibody", "parameter_name": "Rabies antibody", "unit": "IU/ml", "result_type": null, "reference_range": "IMMUNE     : > 0.5  NON IMMUNE : < 0.5", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "EIA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.144991", "updated_at": "2025-07-08T19:27:21.144993"}, {"id": 350, "test_name": "Ricketssia by PCR", "test_code": "001166", "department": "SEROLOGY", "result_name": "Ricketssia by PCR", "parameter_name": "Ricketssia by PCR", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "PCR", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.145086", "updated_at": "2025-07-08T19:27:21.145089"}, {"id": 351, "test_name": "RNA Polymerase III Antibodies, IgG", "test_code": "001631", "department": "SEROLOGY", "result_name": "RNA Polymerase III Antibodies, IgG", "parameter_name": "RNA Polymerase III Antibodies, IgG", "unit": "Units", "result_type": null, "reference_range": "Less than 20", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "ELISA", "specimen_type": "SERUM", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.145184", "updated_at": "2025-07-08T19:27:21.145187"}, {"id": 352, "test_name": "RNP-Sm Antibody", "test_code": "001218", "department": "SEROLOGY", "result_name": "RNP-Sm Antibody", "parameter_name": "RNP-Sm Antibody", "unit": null, "result_type": "Pick List", "reference_range": "0  - 5  : <PERSON><PERSON><PERSON> 6  - 10 : Borderline 11 - 25 : Positive (+) 26 - 50 : Positive (++) >50     : Strong Positive (+++)", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "BLOT", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.145281", "updated_at": "2025-07-08T19:27:21.145284"}, {"id": 353, "test_name": "RPR (Rapid Plasma Reagin)", "test_code": "001167", "department": "SEROLOGY", "result_name": "RPR (Rapid Plasma Reagin)", "parameter_name": "RPR (Rapid Plasma Reagin)", "unit": null, "result_type": "Pick List", "reference_range": "Nonreactive", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.145416", "updated_at": "2025-07-08T19:27:21.145418"}, {"id": 354, "test_name": "Rubella - Avidity test", "test_code": "001273", "department": "SEROLOGY", "result_name": "Rubella - Avidity test", "parameter_name": "Rubella - Avidity test", "unit": "%", "result_type": "Pick List", "reference_range": "Low Avidity : <35 Mean Avidity: 35 - 45 High Avidity: >45", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "EIA", "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.145517", "updated_at": "2025-07-08T19:27:21.145519"}, {"id": 355, "test_name": "RUBELLA IgG", "test_code": "001168", "department": "SEROLOGY", "result_name": "RUBELLA IgG", "parameter_name": "RUBELLA IgG", "unit": "IU/mL", "result_type": null, "reference_range": "Nonreactive : <10.0 Reactive    : >10.0", "critical_low": null, "critical_high": null, "decimal_places": 3, "method": "ECLIA", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.145616", "updated_at": "2025-07-08T19:27:21.145619"}, {"id": 356, "test_name": "RUBELLA IGM", "test_code": "001169", "department": "SEROLOGY", "result_name": "RUBELLA IGM", "parameter_name": "RUBELLA IGM", "unit": "index", "result_type": "Numeric", "reference_range": "Nonreactive  : <0.80 Indeterminate: >=0.80 - <1.00 Reactive     : >= 1.00", "critical_low": null, "critical_high": null, "decimal_places": 3, "method": "ECLIA", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.145715", "updated_at": "2025-07-08T19:27:21.145718"}, {"id": 357, "test_name": "S S A ANTI R-O ANTIBODY", "test_code": "001171", "department": "SEROLOGY", "result_name": "S S A ANTI R-O ANTIBODY", "parameter_name": "S S A ANTI R-O ANTIBODY", "unit": null, "result_type": "Pick List", "reference_range": "0  - 5  : <PERSON><PERSON><PERSON> 6  - 10 : Borderline 11 - 25 : Positive (+) 26 - 50 : Positive (++) >50     : Strong Positive (+++)", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "EIA", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.145813", "updated_at": "2025-07-08T19:27:21.145816"}, {"id": 358, "test_name": "S S B-La ANTIBODY", "test_code": "001172", "department": "SEROLOGY", "result_name": "S S B-La ANTIBODY", "parameter_name": "S S B-La ANTIBODY", "unit": null, "result_type": "Pick List", "reference_range": "0  - 5  : <PERSON><PERSON><PERSON> 6  - 10 : Borderline 11 - 25 : Positive (+) 26 - 50 : Positive (++) >50     : Strong Positive (+++)", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "BLOT", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.145933", "updated_at": "2025-07-08T19:27:21.145937"}, {"id": 359, "test_name": "Salmonella Typhi Dot - IgM", "test_code": "001270", "department": "SEROLOGY", "result_name": "Salmonella Typhi Dot - IgM", "parameter_name": "Salmonella Typhi Dot - IgM", "unit": null, "result_type": "Pick List", "reference_range": "Negative", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IC", "specimen_type": "SERUM", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.146047", "updated_at": "2025-07-08T19:27:21.146049"}, {"id": 360, "test_name": "SARS-CoV-2 IgG", "test_code": "001568", "department": "SEROLOGY", "result_name": "SARS-CoV-2 IgG", "parameter_name": "SARS-CoV-2 IgG", "unit": "index", "result_type": "Pick List", "reference_range": "Negative :<0.9 Equivocal:0.9-1.1 Positive :>1.1 (ELFA)", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELFA", "specimen_type": "SERUM", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.146146", "updated_at": "2025-07-08T19:27:21.146149"}, {"id": 361, "test_name": "SARS-COV-2 IgM", "test_code": "001576", "department": "SEROLOGY", "result_name": "SARS-COV-2 IgM", "parameter_name": "SARS-COV-2 IgM", "unit": "index", "result_type": "Pick List", "reference_range": "Negative : <1.00 Positive : >=1.00\n(ELFA)", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELFA", "specimen_type": "SERUM", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.146246", "updated_at": "2025-07-08T19:27:21.146249"}, {"id": 362, "test_name": "Scl-70 Antibody IgG (DNA Topoisomerase-1)", "test_code": "001220", "department": "SEROLOGY", "result_name": "Scl-70 Antibody IgG (DNA Topoisomerase-1)", "parameter_name": "Scl-70 Antibody IgG (DNA Topoisomerase-1)", "unit": null, "result_type": "Pick List", "reference_range": "0  - 5  : <PERSON><PERSON><PERSON> 6  - 10 : Borderline 11 - 25 : Positive (+) 26 - 50 : Positive (++) >50     : Strong Positive (+++)", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "E.I.A", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.146344", "updated_at": "2025-07-08T19:27:21.146347"}, {"id": 363, "test_name": "SCRUB TYPHUS IGM", "test_code": "001278", "department": "SEROLOGY", "result_name": "SCRUB TYPHUS IGM", "parameter_name": "SCRUB TYPHUS IGM", "unit": null, "result_type": "Pick List", "reference_range": "Less than 9.0 : Negative 9 - 11        : Equivocal More than 11.0: Positive", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELISA", "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.146460", "updated_at": "2025-07-08T19:27:21.146463"}, {"id": 364, "test_name": "SMITH ANTIBODY (SM)", "test_code": "001174", "department": "SEROLOGY", "result_name": "SMITH ANTIBODY (SM)", "parameter_name": "SMITH ANTIBODY (SM)", "unit": "units", "result_type": "Numeric", "reference_range": "<20       -   Negative 20 - 39   -   <PERSON><PERSON> 40 - 80   -   Moderate Positive >80       -   Strong Positive", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELISA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.146577", "updated_at": "2025-07-08T19:27:21.146579"}, {"id": 365, "test_name": "SMOOTH MUSCLE ANTIBODY (ASMA)", "test_code": "001081", "department": "SEROLOGY", "result_name": "SMOOTH MUSCLE ANTIBODY (ASMA)", "parameter_name": "SMOOTH MUSCLE ANTIBODY (ASMA)", "unit": null, "result_type": "Pick List", "reference_range": "NEGATIVE", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IFA", "specimen_type": "Serum", "container": "Serum Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.146675", "updated_at": "2025-07-08T19:27:21.146677"}, {"id": 366, "test_name": "<PERSON><PERSON>phi<PERSON> (VDRL)", "test_code": "001201", "department": "SEROLOGY", "result_name": "<PERSON><PERSON>phi<PERSON> (VDRL)", "parameter_name": "<PERSON><PERSON>phi<PERSON> (VDRL)", "unit": null, "result_type": "Pick List", "reference_range": "Non Reactive", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Immuno Chromatography", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.146774", "updated_at": "2025-07-08T19:27:21.146777"}, {"id": 367, "test_name": "SYPHILIS TOTAL ANTIBODY", "test_code": "001733", "department": "SEROLOGY", "result_name": "SYPHILIS TOTAL ANTIBODY", "parameter_name": "SYPHILIS TOTAL ANTIBODY", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.146870", "updated_at": "2025-07-08T19:27:21.146873"}, {"id": 368, "test_name": "Tissue Transglutaminase (tTG) Ab-IgA", "test_code": "001240", "department": "SEROLOGY", "result_name": "Tissue Transglutaminase (tTG) Ab-IgA", "parameter_name": "Tissue Transglutaminase (tTG) Ab-IgA", "unit": "units", "result_type": "Pick List", "reference_range": "NEGATIVE     : <20 WEAK POSITIVE: 20 -30 POSITIVE     : >30", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "EIA", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.146971", "updated_at": "2025-07-08T19:27:21.146974"}, {"id": 369, "test_name": "Tissue Transglutaminase (tTG) Ab-IgG", "test_code": "001557", "department": "SEROLOGY", "result_name": "Tissue Transglutaminase (tTG) Ab-IgG", "parameter_name": "Tissue Transglutaminase (tTG) Ab-IgG", "unit": "u/mL", "result_type": null, "reference_range": "Less than 4.0 : Negative 4.0-10.0      : Weak positive More than 10.0: Positive", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "EIA", "specimen_type": "SERUM", "container": "Serum Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.147106", "updated_at": "2025-07-08T19:27:21.147109"}, {"id": 370, "test_name": "TORCH IgG", "test_code": "001193", "department": "SEROLOGY", "result_name": "TORCH IgG", "parameter_name": "TORCH IgG", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.147203", "updated_at": "2025-07-08T19:27:21.147206"}, {"id": 371, "test_name": "TORCH IgM", "test_code": "001194", "department": "SEROLOGY", "result_name": "TORCH IgM", "parameter_name": "TORCH IgM", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.147300", "updated_at": "2025-07-08T19:27:21.147303"}, {"id": 372, "test_name": "TOXOPLASMA IgG", "test_code": "001195", "department": "SEROLOGY", "result_name": "TOXOPLASMA IgG", "parameter_name": "TOXOPLASMA IgG", "unit": "IU/mI", "result_type": null, "reference_range": "Non-reactive  : <1.0 Indeterminate : >=1.0 - <3.0 Reactive      : >=3.0", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "ECLIA", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.147398", "updated_at": "2025-07-08T19:27:21.147401"}, {"id": 373, "test_name": "TOXOPLASMA IgM", "test_code": "001196", "department": "SEROLOGY", "result_name": "TOXOPLASMA IgM", "parameter_name": "TOXOPLASMA IgM", "unit": "COI", "result_type": null, "reference_range": "Nonreactive  : <0.80 Indeterminate: >=0.80 - <1.00 Reactive     : >= 1.00", "critical_low": null, "critical_high": null, "decimal_places": 3, "method": "ECLIA", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.147495", "updated_at": "2025-07-08T19:27:21.147497"}, {"id": 374, "test_name": "Toxoplasma, Avidity test", "test_code": "001271", "department": "SEROLOGY", "result_name": "Toxoplasma, Avidity test", "parameter_name": "Toxoplasma, Avidity test", "unit": "%", "result_type": "Pick List", "reference_range": "Low Avidity      : <30  Grayzone Avidity : 30 - 35  High Avidity     : >35", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "EIA", "specimen_type": "SERUM", "container": "Serum Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.147694", "updated_at": "2025-07-08T19:27:21.147699"}, {"id": 375, "test_name": "Treponema  Antibodies", "test_code": "001254", "department": "SEROLOGY", "result_name": "Treponema  Antibodies", "parameter_name": "Treponema  Antibodies", "unit": "\nIndex\n", "result_type": "Pick List", "reference_range": "<=1.0: Negative >1.0 : Positive.", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELISA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.147941", "updated_at": "2025-07-08T19:27:21.147944"}, {"id": 376, "test_name": "TREPONEMA ANTIBODIES", "test_code": "001239", "department": "SEROLOGY", "result_name": "TREPONEMA ANTIBODIES", "parameter_name": "TREPONEMA ANTIBODIES", "unit": "Index", "result_type": null, "reference_range": "<1.0 : Nonreactive  >=1.0: Reactive", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "E.I.A", "specimen_type": "SERUM", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.148057", "updated_at": "2025-07-08T19:27:21.148060"}, {"id": 377, "test_name": "Treponema Pallidum Hemagglutination (TPHA)", "test_code": "001197", "department": "SEROLOGY", "result_name": "Treponema Pallidum Hemagglutination (TPHA)", "parameter_name": "Treponema Pallidum Hemagglutination (TPHA)", "unit": null, "result_type": "Pick List", "reference_range": "NON REACTIVE", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Agglutination", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.148169", "updated_at": "2025-07-08T19:27:21.148172"}, {"id": 378, "test_name": "U1RNP Antibody", "test_code": "001198", "department": "SEROLOGY", "result_name": "U1RNP Antibody", "parameter_name": "U1RNP Antibody", "unit": null, "result_type": "Pick List", "reference_range": "0  - 5  : <PERSON><PERSON><PERSON> 6  - 10 : Borderline 11 - 25 : Positive (+) 26 - 50 : Positive (++) >50     : Strong Positive (+++)", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ELISA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.148321", "updated_at": "2025-07-08T19:27:21.148324"}, {"id": 379, "test_name": "Varicella - Zoster IgG", "test_code": "001200", "department": "SEROLOGY", "result_name": "Varicella - Zoster IgG", "parameter_name": "Varicella - Zoster IgG", "unit": null, "result_type": "Numeric", "reference_range": "Less than 150", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "CLIA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.148430", "updated_at": "2025-07-08T19:27:21.148433"}, {"id": 380, "test_name": "Varicella Z<PERSON>er -IgM antibodies", "test_code": "001199", "department": "SEROLOGY", "result_name": "Varicella Z<PERSON>er -IgM antibodies", "parameter_name": "Varicella Z<PERSON>er -IgM antibodies", "unit": null, "result_type": "Pick List", "reference_range": "Less than 1.0", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "EIA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.148533", "updated_at": "2025-07-08T19:27:21.148535"}, {"id": 381, "test_name": "VGKC (Lgi1 & CASPR2) Antibody", "test_code": "001354", "department": "SEROLOGY", "result_name": "VGKC (Lgi1 & CASPR2) Antibody", "parameter_name": "VGKC (Lgi1 & CASPR2) Antibody", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IFA", "specimen_type": "Serum/CSF", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.148634", "updated_at": "2025-07-08T19:27:21.148637"}, {"id": 382, "test_name": "WEIL FELIX TEST", "test_code": "001202", "department": "SEROLOGY", "result_name": "WEIL FELIX TEST", "parameter_name": "WEIL FELIX TEST", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.148734", "updated_at": "2025-07-08T19:27:21.148742"}, {"id": 383, "test_name": "WESTERN BLOT", "test_code": "001203", "department": "SEROLOGY", "result_name": "WESTERN BLOT", "parameter_name": "WESTERN BLOT", "unit": null, "result_type": null, "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "BLOT", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.148878", "updated_at": "2025-07-08T19:27:21.148881"}, {"id": 384, "test_name": "WESTERN BLOT (HIV-1)", "test_code": "001252", "department": "SEROLOGY", "result_name": "WESTERN BLOT (HIV-1)", "parameter_name": "WESTERN BLOT (HIV-1)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "BLOT", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.148977", "updated_at": "2025-07-08T19:27:21.148979"}, {"id": 385, "test_name": "WIDAL - Tube Dilution", "test_code": "001204", "department": "SEROLOGY", "result_name": "WIDAL - Tube Dilution", "parameter_name": "WIDAL - Tube Dilution", "unit": null, "result_type": null, "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.149078", "updated_at": "2025-07-08T19:27:21.149080"}, {"id": 386, "test_name": "WIDAL-SLIDE METHOD", "test_code": "001212", "department": "SEROLOGY", "result_name": "WIDAL-SLIDE METHOD", "parameter_name": "WIDAL-SLIDE METHOD", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Slide agglutination", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Serology", "is_active": true, "created_at": "2025-07-08T19:27:21.149178", "updated_at": "2025-07-08T19:27:21.149181"}, {"id": 387, "test_name": "BLOOD GROUPING & Rh", "test_code": "000345", "department": "IMMUNOHAEMATOLOGY", "result_name": "BLOOD GROUPING & Rh", "parameter_name": "BLOOD GROUPING & Rh", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Immunohaematology", "is_active": true, "created_at": "2025-07-08T19:27:21.235175", "updated_at": "2025-07-08T19:27:21.235199"}, {"id": 388, "test_name": "BONE  MARROW ASPIRATION.", "test_code": "000447", "department": "IMMUNOHAEMATOLOGY", "result_name": "BONE  MARROW ASPIRATION.", "parameter_name": "BONE  MARROW ASPIRATION.", "unit": null, "result_type": "No Unit / Ref. Value", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "Immunohaematology", "is_active": true, "created_at": "2025-07-08T19:27:21.235365", "updated_at": "2025-07-08T19:27:21.235369"}, {"id": 389, "test_name": "C/S - OTHERS.", "test_code": "001555", "department": "MICROBIOLOGY_SURVEILLANCE", "result_name": "C/S - OTHERS.", "parameter_name": "C/S - OTHERS.", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "MICROBIOLOGY SURVEILLANCE", "is_active": true, "created_at": "2025-07-08T19:27:21.356254", "updated_at": "2025-07-08T19:27:21.356259"}, {"id": 390, "test_name": "Microbiology Surveillance Test (1 Swab)", "test_code": "001347", "department": "MICROBIOLOGY_SURVEILLANCE", "result_name": "Microbiology Surveillance Test (1 Swab)", "parameter_name": "Microbiology Surveillance Test (1 Swab)", "unit": null, "result_type": "Template", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "MICROBIOLOGY SURVEILLANCE", "is_active": true, "created_at": "2025-07-08T19:27:21.356439", "updated_at": "2025-07-08T19:27:21.356442"}, {"id": 391, "test_name": "Microbiology Surveillance Test (Anaerobic)", "test_code": "000738", "department": "MICROBIOLOGY_SURVEILLANCE", "result_name": "Microbiology Surveillance Test (Anaerobic)", "parameter_name": "Microbiology Surveillance Test (Anaerobic)", "unit": null, "result_type": "Template", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "MICROBIOLOGY SURVEILLANCE", "is_active": true, "created_at": "2025-07-08T19:27:21.356559", "updated_at": "2025-07-08T19:27:21.356562"}, {"id": 392, "test_name": "Open End Specimen", "test_code": "001363", "department": "MICROBIOLOGY_SURVEILLANCE", "result_name": "Open End Specimen", "parameter_name": "Open End Specimen", "unit": null, "result_type": "Template", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "MICROBIOLOGY SURVEILLANCE", "is_active": true, "created_at": "2025-07-08T19:27:21.356673", "updated_at": "2025-07-08T19:27:21.356676"}, {"id": 393, "test_name": "Water Culture (Direct & Enrichment)", "test_code": "001346", "department": "MICROBIOLOGY_SURVEILLANCE", "result_name": "Water Culture (Direct & Enrichment)", "parameter_name": "Water Culture (Direct & Enrichment)", "unit": null, "result_type": "Template", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "MICROBIOLOGY SURVEILLANCE", "is_active": true, "created_at": "2025-07-08T19:27:21.356787", "updated_at": "2025-07-08T19:27:21.356790"}, {"id": 394, "test_name": "17 HYDROXY CORTICO STEROID 24 HR URINE", "test_code": "000003", "department": "BIOCHEMISTRY", "result_name": "17 HYDROXY CORTICO STEROID 24 HR URINE", "parameter_name": "17 HYDROXY CORTICO STEROID 24 HR URINE", "unit": "mg/24hrs", "result_type": "Pick List", "reference_range": "< 1 Years     :  <=  1.0 (Both) 1 - 10 Years  :  3 - 6 (Both) > 10 Years    :  3 - 10 (Male)", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Column Chromatography", "specimen_type": "24 H<PERSON> <PERSON><PERSON>", "container": "<PERSON><PERSON><PERSON>er", "instructions": "10 ml of 50% HCL as a preservative, Total volume to be mentioned", "min_sample_qty": "50ml", "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.479596", "updated_at": "2025-07-08T19:27:21.479601"}, {"id": 395, "test_name": "17- KETOSTEROIDS 24 hr.Urine", "test_code": "000228", "department": "BIOCHEMISTRY", "result_name": "17- KETOSTEROIDS 24 hr.Urine", "parameter_name": "17- KETOSTEROIDS 24 hr.Urine", "unit": "mg/24hrs", "result_type": "-", "reference_range": "Infants        :  Upto 1.0 mg/24hrs 3-6 years      :  Upto 3.0 mg/24hrs 7-10 years     :  Upto 4.0 mg/24hrs 10-12yrs (F)   :  Upto 5 mg/24hrs Adolescent(F)  :  3-12 mg/24hrs 10-12yrs (M)   :  Upto 6 mg/24hrs Adolescent(M)  :  3-15 mg/24hrs 1-3 years      :  Upto 2.0 mg/24hrs Adult male     :   10 - 25 mg/24hrs Adult female   :   6 - 14 mg/24hrs", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Column Chromatography", "specimen_type": "24 H<PERSON> <PERSON><PERSON>", "container": "<PERSON><PERSON><PERSON>er", "instructions": "10 ml of 50% (6M) HCL as a preservative, Total volume to be mentioned", "min_sample_qty": "50ml", "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.479747", "updated_at": "2025-07-08T19:27:21.479750"}, {"id": 396, "test_name": "24 hrs URINE PROTEIN CREATININE RATIO", "test_code": "000009", "department": "BIOCHEMISTRY", "result_name": "24 hrs URINE PROTEIN CREATININE RATIO", "parameter_name": "24 hrs URINE PROTEIN CREATININE RATIO", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "URINE", "container": "<PERSON><PERSON>tainer", "instructions": "sample to be collected in Lab container(10% Thymol 5ml), mention total volume", "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.479879", "updated_at": "2025-07-08T19:27:21.479882"}, {"id": 397, "test_name": "Acetylcholine Receptor (AChR) Antibody", "test_code": "000015", "department": "BIOCHEMISTRY", "result_name": "Acetylcholine Receptor (AChR) Antibody", "parameter_name": "Acetylcholine Receptor (AChR) Antibody", "unit": "nmoI/L", "result_type": "Pick List", "reference_range": "<0.40", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "E.I.A", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.479993", "updated_at": "2025-07-08T19:27:21.479996"}, {"id": 398, "test_name": "ACID PHOSPHATASE -PROSTATIC", "test_code": "000016", "department": "BIOCHEMISTRY", "result_name": "ACID PHOSPHATASE -PROSTATIC", "parameter_name": "ACID PHOSPHATASE -PROSTATIC", "unit": null, "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.480102", "updated_at": "2025-07-08T19:27:21.480104"}, {"id": 399, "test_name": "ACID PHOSPHATASE -TOTAL", "test_code": "000210", "department": "BIOCHEMISTRY", "result_name": "ACID PHOSPHATASE -TOTAL", "parameter_name": "ACID PHOSPHATASE -TOTAL", "unit": "U/L", "result_type": "-", "reference_range": "Upto 10.0", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "Colorimetric", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.480211", "updated_at": "2025-07-08T19:27:21.480213"}, {"id": 400, "test_name": "ADA", "test_code": "000018", "department": "BIOCHEMISTRY", "result_name": "ADA", "parameter_name": "ADA", "unit": "U/L", "result_type": "-", "reference_range": "CSF Sample  Normal  : <10.0  Positive: >10.0    <PERSON><PERSON>, Plasma, Pleural,  Pericardil & Ascitic Fluids  Normal         : < 30  Suspect        : 30 - 40  Strong Suspect : >40 - 60   Positive       : >60", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Enzymatic", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.480341", "updated_at": "2025-07-08T19:27:21.480344"}, {"id": 401, "test_name": "ADA (Adenosine Deaminase)", "test_code": "001300", "department": "BIOCHEMISTRY", "result_name": "ADA (Adenosine Deaminase)", "parameter_name": "ADA (Adenosine Deaminase)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Enzymatic", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.480460", "updated_at": "2025-07-08T19:27:21.480462"}, {"id": 402, "test_name": "Alanine aminotransferase (ALT/SGPT)", "test_code": "000149", "department": "BIOCHEMISTRY", "result_name": "Alanine aminotransferase (ALT/SGPT)", "parameter_name": "Alanine aminotransferase (ALT/SGPT)", "unit": "U/L", "result_type": "-", "reference_range": "Female : Less than 33\nMale  : Less than 41", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "IFCC", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.480566", "updated_at": "2025-07-08T19:27:21.480569"}, {"id": 403, "test_name": "Albumin", "test_code": "000025", "department": "BIOCHEMISTRY", "result_name": "Albumin", "parameter_name": "Albumin", "unit": "g/dL", "result_type": "-", "reference_range": "Adult - 3.5 - 5.2", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "Colorimetric: Bromocresol Green (BCG)", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.480672", "updated_at": "2025-07-08T19:27:21.480675"}, {"id": 404, "test_name": "Albumin/Globulin", "test_code": "000297", "department": "BIOCHEMISTRY", "result_name": "Albumin/Globulin", "parameter_name": "Albumin/Globulin", "unit": "<PERSON><PERSON>", "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Calculated", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.480776", "updated_at": "2025-07-08T19:27:21.480779"}, {"id": 405, "test_name": "ALCOHOL IN SERUM (Ethanol)", "test_code": "000026", "department": "BIOCHEMISTRY", "result_name": "ALCOHOL IN SERUM (Ethanol)", "parameter_name": "ALCOHOL IN SERUM (Ethanol)", "unit": "mg/dL", "result_type": "Pick List", "reference_range": "Not detected", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Enzymatic Method", "specimen_type": "SERUM", "container": "PLAIN", "instructions": "DO NOT USE ANY ALCOHOL FOR <PERSON><PERSON>ANING WHILE COLLECTING BLOOD SAMPLE", "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.480905", "updated_at": "2025-07-08T19:27:21.480908"}, {"id": 406, "test_name": "Aldolase", "test_code": "000250", "department": "BIOCHEMISTRY", "result_name": "Aldolase", "parameter_name": "Aldolase", "unit": "U/L", "result_type": "-", "reference_range": "<7.40", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Enzymatic", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.481023", "updated_at": "2025-07-08T19:27:21.481026"}, {"id": 407, "test_name": "Alkaline phosphatase", "test_code": "000027", "department": "BIOCHEMISTRY", "result_name": "Alkaline phosphatase", "parameter_name": "Alkaline phosphatase", "unit": "U/L", "result_type": "-", "reference_range": "Children : 47 - 406 (Age and gender dependent)  Adults   : 80 - 306", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "PNPP-DGKC", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.481126", "updated_at": "2025-07-08T19:27:21.481129"}, {"id": 408, "test_name": "Amino leveulinic acid (ALA)", "test_code": "000245", "department": "BIOCHEMISTRY", "result_name": "Amino leveulinic acid (ALA)", "parameter_name": "Amino leveulinic acid (ALA)", "unit": "mg/dL", "result_type": "-", "reference_range": "1 - 7", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "Column Chromatography", "specimen_type": "SPOT URINE", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.481229", "updated_at": "2025-07-08T19:27:21.481231"}, {"id": 409, "test_name": "AMMONIA", "test_code": "000028", "department": "BIOCHEMISTRY", "result_name": "AMMONIA", "parameter_name": "AMMONIA", "unit": "umoI/L", "result_type": "-", "reference_range": "Male   : 16 - 60 Female : 11 - 51", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Colorimetric", "specimen_type": "EDTA PLASMA", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.481332", "updated_at": "2025-07-08T19:27:21.481335"}, {"id": 410, "test_name": "<PERSON><PERSON>", "test_code": "000029", "department": "BIOCHEMISTRY", "result_name": "<PERSON><PERSON>", "parameter_name": "<PERSON><PERSON>", "unit": "U/L", "result_type": "-", "reference_range": "Adult : 28 - 100", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "CNPG3", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.481458", "updated_at": "2025-07-08T19:27:21.481460"}, {"id": 411, "test_name": "AMYLASE  (Body Fluid)", "test_code": "000033", "department": "BIOCHEMISTRY", "result_name": "AMYLASE  (Body Fluid)", "parameter_name": "AMYLASE  (Body Fluid)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "Body Fluids", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.481573", "updated_at": "2025-07-08T19:27:21.481576"}, {"id": 412, "test_name": "Amyloid A", "test_code": "001594", "department": "BIOCHEMISTRY", "result_name": "Amyloid A", "parameter_name": "Amyloid A", "unit": "mg/L", "result_type": "Numeric", "reference_range": "Less than 6.4", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "NEPHELOMETRY", "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.481677", "updated_at": "2025-07-08T19:27:21.481680"}, {"id": 413, "test_name": "Angiotensin Converting enzyme (ACE)", "test_code": "000295", "department": "BIOCHEMISTRY", "result_name": "Angiotensin Converting enzyme (ACE)", "parameter_name": "Angiotensin Converting enzyme (ACE)", "unit": "U/L", "result_type": "Numeric", "reference_range": "Serum : 8 - 52", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "Spectrophotometry", "specimen_type": "Serum/CSF", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.481779", "updated_at": "2025-07-08T19:27:21.481782"}, {"id": 414, "test_name": "Anion gap", "test_code": "001329", "department": "BIOCHEMISTRY", "result_name": "Anion gap", "parameter_name": "Anion gap", "unit": "mmoI/L", "result_type": "Numeric", "reference_range": "10 - 20", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Calculated", "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.481881", "updated_at": "2025-07-08T19:27:21.481883"}, {"id": 415, "test_name": "APOLIPOPROTEIN A1", "test_code": "000030", "department": "BIOCHEMISTRY", "result_name": "APOLIPOPROTEIN A1", "parameter_name": "APOLIPOPROTEIN A1", "unit": "mg/dL", "result_type": "Numeric", "reference_range": "MALE   : 104 - 202 FEMALE : 108 - 225", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "NEPHELOMETRY", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.482006", "updated_at": "2025-07-08T19:27:21.482009"}, {"id": 416, "test_name": "APOLIPOPROTEIN B", "test_code": "000031", "department": "BIOCHEMISTRY", "result_name": "APOLIPOPROTEIN B", "parameter_name": "APOLIPOPROTEIN B", "unit": "mg/dL", "result_type": "Numeric", "reference_range": "Male : 55 - 140 Female : 55 - 125", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "NEPHELOMETRY", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.482126", "updated_at": "2025-07-08T19:27:21.482128"}, {"id": 417, "test_name": "Gamma Glutamyl-Transferase (GGT)", "test_code": "000079", "department": "BIOCHEMISTRY", "result_name": "Gamma Glutamyl-Transferase (GGT)", "parameter_name": "Gamma Glutamyl-Transferase (GGT)", "unit": "U/L", "result_type": null, "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "Carboxy Substrate", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.482229", "updated_at": "2025-07-08T19:27:21.482232"}, {"id": 418, "test_name": "Aspartate aminotransferase (AST/SGOT)", "test_code": "000148", "department": "BIOCHEMISTRY", "result_name": "Aspartate aminotransferase (AST/SGOT)", "parameter_name": "Aspartate aminotransferase (AST/SGOT)", "unit": "U/L", "result_type": "Numeric", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "IFCC", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.482333", "updated_at": "2025-07-08T19:27:21.482336"}, {"id": 419, "test_name": "BENZODIAZAPINE", "test_code": "000247", "department": "BIOCHEMISTRY", "result_name": "BENZODIAZAPINE", "parameter_name": "BENZODIAZAPINE", "unit": "ng/ml", "result_type": "Pick List", "reference_range": "NEGATIVE : <200 POSITIVE : >=200", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Immuno Chromatography", "specimen_type": "URINE", "container": "<PERSON><PERSON>tainer", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.482436", "updated_at": "2025-07-08T19:27:21.482438"}, {"id": 420, "test_name": "Bicarbonate", "test_code": "000038", "department": "BIOCHEMISTRY", "result_name": "Bicarbonate", "parameter_name": "Bicarbonate", "unit": "mmoI/L", "result_type": "Numeric", "reference_range": "Adult : 23 - 30 >60y : 23- 31 >90y : 20-29", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Manual : Phosphoenol Pyruvate Carboxylase", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.482560", "updated_at": "2025-07-08T19:27:21.482563"}, {"id": 421, "test_name": "Bile acids - Total", "test_code": "000271", "department": "BIOCHEMISTRY", "result_name": "Bile acids - Total", "parameter_name": "Bile acids - Total", "unit": "umoI/L", "result_type": "Numeric", "reference_range": "0.5 - 10.0", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "Enzymatic", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.482678", "updated_at": "2025-07-08T19:27:21.482681"}, {"id": 422, "test_name": "<PERSON><PERSON><PERSON><PERSON>, Direct", "test_code": "000217", "department": "BIOCHEMISTRY", "result_name": "<PERSON><PERSON><PERSON><PERSON>, Direct", "parameter_name": "<PERSON><PERSON><PERSON><PERSON>, Direct", "unit": "mg/dL", "result_type": "Numeric", "reference_range": "0.0 - 0.2", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "DCA", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.482782", "updated_at": "2025-07-08T19:27:21.482785"}, {"id": 423, "test_name": "Bilirubin, Indirect", "test_code": "000231", "department": "BIOCHEMISTRY", "result_name": "Bilirubin, Indirect", "parameter_name": "Bilirubin, Indirect", "unit": "mg/dL", "result_type": "Calculated", "reference_range": "0.1 - 1.0", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "Calculated", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.482886", "updated_at": "2025-07-08T19:27:21.482888"}, {"id": 424, "test_name": "<PERSON><PERSON><PERSON><PERSON>, Total", "test_code": "000216", "department": "BIOCHEMISTRY", "result_name": "<PERSON><PERSON><PERSON><PERSON>, Total", "parameter_name": "<PERSON><PERSON><PERSON><PERSON>, Total", "unit": "mg/dL", "result_type": "Pick List", "reference_range": "Premature:  Cord       : < 2.0 0 - 1 day  :<8.0 1 - 2 days : <12.0 3 - 5 days : <16.0  Full term:  Cord       : < 2.0 0 - 1 day  : 1.4 - 8.7 1 - 2 days : 3.4 - 11.5 3 - 5 days : 1.5 - 12  >5 days - 60y      : 0.3 - 1.2 60 - 90 y : 0.2 - 1.1 >90y : 0.2 - 0.9", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "DCA", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.482990", "updated_at": "2025-07-08T19:27:21.482992"}, {"id": 425, "test_name": "Biotinidase", "test_code": "000281", "department": "BIOCHEMISTRY", "result_name": "Biotinidase", "parameter_name": "Biotinidase", "unit": "nmoI/min/ML", "result_type": null, "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Enzymatic", "specimen_type": "BLOOD", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.483113", "updated_at": "2025-07-08T19:27:21.483116"}, {"id": 426, "test_name": "Blood Urea Nitrogen (BUN)", "test_code": "000419", "department": "BIOCHEMISTRY", "result_name": "Blood Urea Nitrogen (BUN)", "parameter_name": "Blood Urea Nitrogen (BUN)", "unit": "mg/dL", "result_type": "Numeric", "reference_range": "6.9-18.0", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Enzymatic", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.483233", "updated_at": "2025-07-08T19:27:21.483236"}, {"id": 427, "test_name": "BUN", "test_code": "000215", "department": "BIOCHEMISTRY", "result_name": "BUN", "parameter_name": "BUN", "unit": "mg/dL", "result_type": "Numeric", "reference_range": "6.9-18.0", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Enzymatic", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.483338", "updated_at": "2025-07-08T19:27:21.483340"}, {"id": 428, "test_name": "BUN - URINE", "test_code": "001703", "department": "BIOCHEMISTRY", "result_name": "BUN - URINE", "parameter_name": "BUN - URINE", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.483437", "updated_at": "2025-07-08T19:27:21.483440"}, {"id": 429, "test_name": "Bun/Creatinine", "test_code": "001364", "department": "BIOCHEMISTRY", "result_name": "Bun/Creatinine", "parameter_name": "Bun/Creatinine", "unit": null, "result_type": "Calculated", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Calculated", "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.483538", "updated_at": "2025-07-08T19:27:21.483541"}, {"id": 430, "test_name": "Calcium", "test_code": "000208", "department": "BIOCHEMISTRY", "result_name": "Calcium", "parameter_name": "Calcium", "unit": "mg/dL", "result_type": "Numeric", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "End point : Arsenazo III", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.483661", "updated_at": "2025-07-08T19:27:21.483663"}, {"id": 431, "test_name": "Calcium -  Urine", "test_code": "000179", "department": "BIOCHEMISTRY", "result_name": "Calcium -  Urine", "parameter_name": "Calcium -  Urine", "unit": "mg/dL", "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Colorimetric : 5-nitro-5’-methyl-BAPTA", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.483778", "updated_at": "2025-07-08T19:27:21.483780"}, {"id": 432, "test_name": "Calcium, Urine 24Hr", "test_code": "000040", "department": "BIOCHEMISTRY", "result_name": "Calcium, Urine 24Hr", "parameter_name": "Calcium, Urine 24Hr", "unit": null, "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Ion Selective Electrode", "specimen_type": "<PERSON><PERSON>", "container": "<PERSON><PERSON><PERSON>er", "instructions": "Sample to be collected in Lab container(10 % Thymol 5ml), mention total volume", "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.483881", "updated_at": "2025-07-08T19:27:21.483883"}, {"id": 433, "test_name": "Cannabis (Marijuana).", "test_code": "000275", "department": "BIOCHEMISTRY", "result_name": "Cannabis (Marijuana).", "parameter_name": "Cannabis (Marijuana).", "unit": "ng/ml", "result_type": "Pick List", "reference_range": "Negative : < 50", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IC", "specimen_type": "URINE", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.483984", "updated_at": "2025-07-08T19:27:21.483987"}, {"id": 434, "test_name": "Calculus Study", "test_code": "000282", "department": "GENERAL", "result_name": "Calculus Study", "parameter_name": "Calculus Study", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.484083", "updated_at": "2025-07-08T19:27:21.484085"}, {"id": 435, "test_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "test_code": "000041", "department": "BIOCHEMISTRY", "result_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parameter_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unit": "g/L", "result_type": "-", "reference_range": "0.2 - 0.6", "critical_low": null, "critical_high": null, "decimal_places": 3, "method": "NEPHELOMETRY", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.484187", "updated_at": "2025-07-08T19:27:21.484191"}, {"id": 436, "test_name": "CHLORIDE - PLEURAL  FLUID", "test_code": "000045", "department": "BIOCHEMISTRY", "result_name": "CHLORIDE - PLEURAL  FLUID", "parameter_name": "CHLORIDE - PLEURAL  FLUID", "unit": "mEq/L", "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ISE Indirect", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.484326", "updated_at": "2025-07-08T19:27:21.484329"}, {"id": 437, "test_name": "CHLORIDE SYNOVIAL FLUID", "test_code": "000048", "department": "BIOCHEMISTRY", "result_name": "CHLORIDE SYNOVIAL FLUID", "parameter_name": "CHLORIDE SYNOVIAL FLUID", "unit": "mEq/L", "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ISE Indirect", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.484425", "updated_at": "2025-07-08T19:27:21.484428"}, {"id": 438, "test_name": "Chloride, CSF", "test_code": "000043", "department": "BIOCHEMISTRY", "result_name": "Chloride, CSF", "parameter_name": "Chloride, CSF", "unit": "mmoI/L", "result_type": "-", "reference_range": "115 - 130", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Ion Selective Electrode", "specimen_type": "Cerebrospinal fluid", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.484527", "updated_at": "2025-07-08T19:27:21.484529"}, {"id": 439, "test_name": "Chloride, Urine", "test_code": "000230", "department": "BIOCHEMISTRY", "result_name": "Chloride, Urine", "parameter_name": "Chloride, Urine", "unit": "mmoI/L", "result_type": "Pick List", "reference_range": "Adult (< 40 Yr) Male  : 27 - 371 Female: 20 - 295  Adult (= 40 Yr) Male  : 30 - 260 Female: 24 - 225", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Ion Selective Electrode", "specimen_type": "<PERSON><PERSON>", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.484626", "updated_at": "2025-07-08T19:27:21.484629"}, {"id": 440, "test_name": "Chloride, Urine 24 Hrs", "test_code": "000044", "department": "BIOCHEMISTRY", "result_name": "Chloride, Urine 24 Hrs", "parameter_name": "Chloride, Urine 24 Hrs", "unit": null, "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Ion Selective Electrode", "specimen_type": "<PERSON><PERSON>", "container": "<PERSON><PERSON><PERSON>er", "instructions": "Sample to be collected in Lab container(10 % Thymol 5ml), mention total volume", "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.484728", "updated_at": "2025-07-08T19:27:21.484731"}, {"id": 441, "test_name": "Chloride", "test_code": "000207", "department": "BIOCHEMISTRY", "result_name": "Chloride", "parameter_name": "Chloride", "unit": "mmoI/L", "result_type": "-", "reference_range": "Cord       : 96 - 104 Premature  : 95 - 110 0 - 30 days: 98 - 113 Adult      : 98 - 107 >90 years  : 98 - 111", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Ion Selective Electrode", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.484926", "updated_at": "2025-07-08T19:27:21.484929"}, {"id": 442, "test_name": "Cholesterol, HDL", "test_code": "000087", "department": "BIOCHEMISTRY", "result_name": "Cholesterol, HDL", "parameter_name": "Cholesterol, HDL", "unit": "mg/dL", "result_type": "-", "reference_range": "Low  : < 40 High : >=60", "critical_low": 15.0, "critical_high": 80.0, "decimal_places": 1, "method": "Direct", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.485155", "updated_at": "2025-07-08T19:27:21.485159"}, {"id": 443, "test_name": "Cholesterol, LDL", "test_code": "000100", "department": "BIOCHEMISTRY", "result_name": "Cholesterol, LDL", "parameter_name": "Cholesterol, LDL", "unit": "mg/dL", "result_type": "-", "reference_range": "Children (ACC 2018) Acceptable: <110 Borderline: 110-129 Abnormal  : >=130  Adult (NCEP ATP-III) Optimal               : <100 Near or above optimal : 100 - 129 Borderline high       : 130 - 159 High                  : 160 - 189 Very high             : >=190", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Direct", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.485325", "updated_at": "2025-07-08T19:27:21.485328"}, {"id": 444, "test_name": "Cholesterol, LDL (Direct)", "test_code": "000242", "department": "BIOCHEMISTRY", "result_name": "Cholesterol, LDL (Direct)", "parameter_name": "Cholesterol, LDL (Direct)", "unit": "mg/dL", "result_type": "-", "reference_range": "Optimal               : <100 Near or above optimal : 100 - 129 Borderline high       : 130 - 159 High                  : 160 - 189 Very high             : >190", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "Direct", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.487001", "updated_at": "2025-07-08T19:27:21.487025"}, {"id": 445, "test_name": "<PERSON><PERSON><PERSON><PERSON>, Total", "test_code": "000050", "department": "BIOCHEMISTRY", "result_name": "<PERSON><PERSON><PERSON><PERSON>, Total", "parameter_name": "<PERSON><PERSON><PERSON><PERSON>, Total", "unit": "mg/dL", "result_type": "-", "reference_range": "CHILDREN (ACC 2018) Acceptable: <170 Borderline: 170-199 Abnormal  : >=200  ADULT (NCEP ATP-III) Desirable          : <200 Borderline high : 200 - 239 High                : >239", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Enzymatic : CHOD-PAP", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.487320", "updated_at": "2025-07-08T19:27:21.487324"}, {"id": 446, "test_name": "Cholesterol, VLDL", "test_code": "000203", "department": "BIOCHEMISTRY", "result_name": "Cholesterol, VLDL", "parameter_name": "Cholesterol, VLDL", "unit": "mg/dL", "result_type": "-", "reference_range": "Less than 30  (NCEP ATP-III)", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Calculation", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.487721", "updated_at": "2025-07-08T19:27:21.487727"}, {"id": 447, "test_name": "Cholesterol/HDL Ratio", "test_code": "000298", "department": "BIOCHEMISTRY", "result_name": "Cholesterol/HDL Ratio", "parameter_name": "Cholesterol/HDL Ratio", "unit": null, "result_type": "-", "reference_range": "Castelli's Risk Index -I Ideal : <3.5 Good: 3.5-5.0 High: >=5", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Calculation", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.488055", "updated_at": "2025-07-08T19:27:21.488058"}, {"id": 448, "test_name": "Cholinesterase", "test_code": "000049", "department": "BIOCHEMISTRY", "result_name": "Cholinesterase", "parameter_name": "Cholinesterase", "unit": "U/L", "result_type": "-", "reference_range": "2180 - 9180", "critical_low": 1500.0, "critical_high": 20000.0, "decimal_places": 0, "method": "Enzymatic", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.488213", "updated_at": "2025-07-08T19:27:21.488216"}, {"id": 449, "test_name": "Chylomicron, Qualitative", "test_code": "000285", "department": "BIOCHEMISTRY", "result_name": "Chylomicron, Qualitative", "parameter_name": "Chylomicron, Qualitative", "unit": null, "result_type": "Pick List", "reference_range": "NEGATIVE", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "<PERSON><PERSON>", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.488327", "updated_at": "2025-07-08T19:27:21.488329"}, {"id": 450, "test_name": "Citrate, Urine 24 Hrs", "test_code": "000290", "department": "BIOCHEMISTRY", "result_name": "Citrate, Urine 24 Hrs", "parameter_name": "Citrate, Urine 24 Hrs", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "24 H<PERSON> <PERSON><PERSON>", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.488549", "updated_at": "2025-07-08T19:27:21.488552"}, {"id": 451, "test_name": "CK-MB", "test_code": "001645", "department": "BIOCHEMISTRY", "result_name": "CK-MB", "parameter_name": "CK-MB", "unit": "U/L", "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 1, "method": null, "specimen_type": "SERUM", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.488674", "updated_at": "2025-07-08T19:27:21.488677"}, {"id": 452, "test_name": "CLOZAPINE WITH NOR-CLOZAPINE", "test_code": "001432", "department": "BIOCHEMISTRY", "result_name": "CLOZAPINE WITH NOR-CLOZAPINE", "parameter_name": "CLOZAPINE WITH NOR-CLOZAPINE", "unit": null, "result_type": "Template", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "LC-MS/MS", "specimen_type": "SERUM", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.488780", "updated_at": "2025-07-08T19:27:21.488783"}, {"id": 453, "test_name": "COMPLEMENT C3", "test_code": "000052", "department": "BIOCHEMISTRY", "result_name": "COMPLEMENT C3", "parameter_name": "COMPLEMENT C3", "unit": "mg/dL", "result_type": "-", "reference_range": "90 - 180", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "NEPHELOMETRY", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.488889", "updated_at": "2025-07-08T19:27:21.488892"}, {"id": 454, "test_name": "COMPLEMENT C4", "test_code": "000053", "department": "BIOCHEMISTRY", "result_name": "COMPLEMENT C4", "parameter_name": "COMPLEMENT C4", "unit": "mg/dL", "result_type": "-", "reference_range": "9.0 - 36.0", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "NEPHELOMETRY", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.488997", "updated_at": "2025-07-08T19:27:21.488999"}, {"id": 455, "test_name": "CPK", "test_code": "000054", "department": "BIOCHEMISTRY", "result_name": "CPK", "parameter_name": "CPK", "unit": "U/L", "result_type": "-", "reference_range": "MALE   : 46 - 171 FEMALE : 35 - 145", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "IFCC", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.489217", "updated_at": "2025-07-08T19:27:21.489220"}, {"id": 456, "test_name": "CREATININE CLEARENCE- 24 HR URINE", "test_code": "000058", "department": "BIOCHEMISTRY", "result_name": "CREATININE CLEARENCE- 24 HR URINE", "parameter_name": "CREATININE CLEARENCE- 24 HR URINE", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.489347", "updated_at": "2025-07-08T19:27:21.489349"}, {"id": 457, "test_name": "C<PERSON><PERSON>ine, Urine", "test_code": "000182", "department": "BIOCHEMISTRY", "result_name": "C<PERSON><PERSON>ine, Urine", "parameter_name": "C<PERSON><PERSON>ine, Urine", "unit": "mg/dL", "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Enzymatic Method", "specimen_type": "<PERSON><PERSON>", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.489451", "updated_at": "2025-07-08T19:27:21.489454"}, {"id": 458, "test_name": "C<PERSON><PERSON><PERSON>, Urine 24Hr", "test_code": "000225", "department": "BIOCHEMISTRY", "result_name": "C<PERSON><PERSON><PERSON>, Urine 24Hr", "parameter_name": "C<PERSON><PERSON><PERSON>, Urine 24Hr", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.489552", "updated_at": "2025-07-08T19:27:21.489554"}, {"id": 459, "test_name": "Creatinine", "test_code": "000057", "department": "BIOCHEMISTRY", "result_name": "Creatinine", "parameter_name": "Creatinine", "unit": "mg/dL", "result_type": "-", "reference_range": "0.67 - 1.17 (SYS-400 OF DIASYS)", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Enzymatic", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.489678", "updated_at": "2025-07-08T19:27:21.489680"}, {"id": 460, "test_name": "CRP", "test_code": "001103", "department": "BIOCHEMISTRY", "result_name": "CRP", "parameter_name": "CRP", "unit": "mg/dL", "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "NEPHELOMETRY", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.489976", "updated_at": "2025-07-08T19:27:21.489980"}, {"id": 461, "test_name": "CSF Index", "test_code": "001501", "department": "BIOCHEMISTRY", "result_name": "CSF Index", "parameter_name": "CSF Index", "unit": null, "result_type": "Template", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.490188", "updated_at": "2025-07-08T19:27:21.490192"}, {"id": 462, "test_name": "Cyclosporin (C2)", "test_code": "000274", "department": "BIOCHEMISTRY", "result_name": "Cyclosporin (C2)", "parameter_name": "Cyclosporin (C2)", "unit": "ug/L", "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "CMIA", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.490446", "updated_at": "2025-07-08T19:27:21.490462"}, {"id": 463, "test_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (CO)", "test_code": "000260", "department": "BIOCHEMISTRY", "result_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (CO)", "parameter_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (CO)", "unit": "ug/L", "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "LC-MS/MS", "specimen_type": "EDTA BLOOD", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.490582", "updated_at": "2025-07-08T19:27:21.490585"}, {"id": 464, "test_name": "CYSTATIN-C", "test_code": "000248", "department": "BIOCHEMISTRY", "result_name": "CYSTATIN-C", "parameter_name": "CYSTATIN-C", "unit": "mg/L", "result_type": "-", "reference_range": "0.53 - 0.95", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "NEPHELOMETRY", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.490701", "updated_at": "2025-07-08T19:27:21.490704"}, {"id": 465, "test_name": "D-Dimer (Quantitative)", "test_code": "000061", "department": "BIOCHEMISTRY", "result_name": "D-Dimer (Quantitative)", "parameter_name": "D-Dimer (Quantitative)", "unit": "microgm/mL", "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "NEPHELOMETRY", "specimen_type": "Citrate Plasma", "container": "Citrate Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.490815", "updated_at": "2025-07-08T19:27:21.490818"}, {"id": 466, "test_name": "Determination of  Arsenic", "test_code": "000940", "department": "BIOCHEMISTRY", "result_name": "Determination of  Arsenic", "parameter_name": "Determination of  Arsenic", "unit": "ug/L", "result_type": "-", "reference_range": "0.4-11.9", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "ICPMS", "specimen_type": "WHOLE BLOOD", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.490926", "updated_at": "2025-07-08T19:27:21.490929"}, {"id": 467, "test_name": "ELECTROPHORESIS OF CSF", "test_code": "000073", "department": "BIOCHEMISTRY", "result_name": "ELECTROPHORESIS OF CSF", "parameter_name": "ELECTROPHORESIS OF CSF", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.491159", "updated_at": "2025-07-08T19:27:21.491162"}, {"id": 468, "test_name": "ELECTROPHORESIS- 24 HRS URINE", "test_code": "000074", "department": "BIOCHEMISTRY", "result_name": "ELECTROPHORESIS- 24 HRS URINE", "parameter_name": "ELECTROPHORESIS- 24 HRS URINE", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Gel Electrophoresis - High resolution", "specimen_type": "24 H<PERSON> <PERSON><PERSON>", "container": "Culture Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.491366", "updated_at": "2025-07-08T19:27:21.491369"}, {"id": 469, "test_name": "DRUGS OF ABUSE - 10 Drugs", "test_code": "001512", "department": "BIOCHEMISTRY", "result_name": "DRUGS OF ABUSE - 10 Drugs", "parameter_name": "DRUGS OF ABUSE - 10 Drugs", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.491491", "updated_at": "2025-07-08T19:27:21.491494"}, {"id": 470, "test_name": "DRUGS OF ABUSE - 12 Drugs", "test_code": "001407", "department": "BIOCHEMISTRY", "result_name": "DRUGS OF ABUSE - 12 Drugs", "parameter_name": "DRUGS OF ABUSE - 12 Drugs", "unit": null, "result_type": "Template", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IC", "specimen_type": "URINE", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.491782", "updated_at": "2025-07-08T19:27:21.491786"}, {"id": 471, "test_name": "DRUGS OF ABUSE - 5 Drugs", "test_code": "001510", "department": "BIOCHEMISTRY", "result_name": "DRUGS OF ABUSE - 5 Drugs", "parameter_name": "DRUGS OF ABUSE - 5 Drugs", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IC", "specimen_type": "URINE", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.491939", "updated_at": "2025-07-08T19:27:21.491942"}, {"id": 472, "test_name": "DRUGS OF ABUSE - 9 Drugs", "test_code": "000255", "department": "BIOCHEMISTRY", "result_name": "DRUGS OF ABUSE - 9 Drugs", "parameter_name": "DRUGS OF ABUSE - 9 Drugs", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Immuno Chromatography", "specimen_type": "URINE", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.492059", "updated_at": "2025-07-08T19:27:21.492062"}, {"id": 473, "test_name": "Electrophoresis - HB", "test_code": "000072", "department": "BIOCHEMISTRY", "result_name": "Electrophoresis - HB", "parameter_name": "Electrophoresis - HB", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "HPLC", "specimen_type": "WHOLE BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.492174", "updated_at": "2025-07-08T19:27:21.492177"}, {"id": 474, "test_name": "ELECTROPHORESIS OF CSF", "test_code": "000073", "department": "BIOCHEMISTRY", "result_name": "ELECTROPHORESIS OF CSF", "parameter_name": "ELECTROPHORESIS OF CSF", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.492283", "updated_at": "2025-07-08T19:27:21.492285"}, {"id": 475, "test_name": "ELECTROPHORESIS- 24 HRS URINE", "test_code": "000074", "department": "BIOCHEMISTRY", "result_name": "ELECTROPHORESIS- 24 HRS URINE", "parameter_name": "ELECTROPHORESIS- 24 HRS URINE", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.492509", "updated_at": "2025-07-08T19:27:21.492512"}, {"id": 476, "test_name": "ELECTROPHORESIS-PROTEIN", "test_code": "000219", "department": "BIOCHEMISTRY", "result_name": "ELECTROPHORESIS-PROTEIN", "parameter_name": "ELECTROPHORESIS-PROTEIN", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Serum", "specimen_type": null, "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.492633", "updated_at": "2025-07-08T19:27:21.492636"}, {"id": 477, "test_name": "Est. Glomerular Filtration Rate", "test_code": "001411", "department": "BIOCHEMISTRY", "result_name": "Est. Glomerular Filtration Rate", "parameter_name": "Est. Glomerular Filtration Rate", "unit": "ml/min", "result_type": "-", "reference_range": ">= 90.0 : Normal 60 - 89 : Mild decrease 30 - 59 : Moderate decrease 15 - 29 : Severe decrease <15     : Kidney failure", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Calculation", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.492743", "updated_at": "2025-07-08T19:27:21.492746"}, {"id": 478, "test_name": "EVEROLIMUS", "test_code": "001574", "department": "BIOCHEMISTRY", "result_name": "EVEROLIMUS", "parameter_name": "EVEROLIMUS", "unit": "ng/ml", "result_type": "Pick List", "reference_range": "Refer Interpretation", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "LC-MS/MS", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.492850", "updated_at": "2025-07-08T19:27:21.492852"}, {"id": 479, "test_name": "FDP", "test_code": "000076", "department": "BIOCHEMISTRY", "result_name": "FDP", "parameter_name": "FDP", "unit": null, "result_type": "Pick List", "reference_range": "Negative", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Agglutination", "specimen_type": "Citrate Plasma", "container": "Citrate Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.492956", "updated_at": "2025-07-08T19:27:21.492958"}, {"id": 480, "test_name": "G6PD LEVEL", "test_code": "000356", "department": "BIOCHEMISTRY", "result_name": "G6PD LEVEL", "parameter_name": "G6PD LEVEL", "unit": null, "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.493127", "updated_at": "2025-07-08T19:27:21.493130"}, {"id": 481, "test_name": "Gamma Glutamyl-Transferase (GGT)", "test_code": "000079", "department": "BIOCHEMISTRY", "result_name": "Gamma Glutamyl-Transferase (GGT)", "parameter_name": "Gamma Glutamyl-Transferase (GGT)", "unit": "U/L", "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "Carboxy Substrate", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.493302", "updated_at": "2025-07-08T19:27:21.493304"}, {"id": 482, "test_name": "GCT (100 GMS)", "test_code": "000080", "department": "BIOCHEMISTRY", "result_name": "GCT (100 GMS)", "parameter_name": "GCT (100 GMS)", "unit": "mg/dL", "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Colorimetric : GOD-POD", "specimen_type": "FLURIDE PLASMA", "container": "<PERSON>", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.493406", "updated_at": "2025-07-08T19:27:21.493409"}, {"id": 483, "test_name": "GCT (50 gms)", "test_code": "000084", "department": "BIOCHEMISTRY", "result_name": "GCT (50 gms)", "parameter_name": "GCT (50 gms)", "unit": "mg/dL", "result_type": "-", "reference_range": "< 140", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Colorimetric : GOD-POD", "specimen_type": "Plasma", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.493512", "updated_at": "2025-07-08T19:27:21.493514"}, {"id": 484, "test_name": "GCT (75 gms)", "test_code": "000284", "department": "BIOCHEMISTRY", "result_name": "GCT (75 gms)", "parameter_name": "GCT (75 gms)", "unit": null, "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Colorimetric : GOD-POD", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.493615", "updated_at": "2025-07-08T19:27:21.493618"}, {"id": 485, "test_name": "Globulin", "test_code": "000081", "department": "BIOCHEMISTRY", "result_name": "Globulin", "parameter_name": "Globulin", "unit": "g/dL", "result_type": "-", "reference_range": "2.0-3.9", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "Calculated", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.493724", "updated_at": "2025-07-08T19:27:21.493726"}, {"id": 486, "test_name": "Glomerular Filtration Rate (eGFR)", "test_code": "000256", "department": "BIOCHEMISTRY", "result_name": "Glomerular Filtration Rate (eGFR)", "parameter_name": "Glomerular Filtration Rate (eGFR)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.493959", "updated_at": "2025-07-08T19:27:21.493961"}, {"id": 487, "test_name": "<PERSON><PERSON>", "test_code": "000029", "department": "BIOCHEMISTRY", "result_name": "<PERSON><PERSON>", "parameter_name": "<PERSON><PERSON>", "unit": "U/L", "result_type": "-", "reference_range": "Adult : 28 - 100", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "CNPG3", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.494065", "updated_at": "2025-07-08T19:27:21.494067"}, {"id": 488, "test_name": "APOLIPOPROTEIN A1", "test_code": "000030", "department": "BIOCHEMISTRY", "result_name": "APOLIPOPROTEIN A1", "parameter_name": "APOLIPOPROTEIN A1", "unit": "mg/dL", "result_type": "-", "reference_range": "MALE   : 104 - 202 FEMALE : 108 - 225", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "NEPHELOMETRY", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.494170", "updated_at": "2025-07-08T19:27:21.494173"}, {"id": 489, "test_name": "Alkaline phosphatase", "test_code": "000027", "department": "BIOCHEMISTRY", "result_name": "Alkaline phosphatase", "parameter_name": "Alkaline phosphatase", "unit": "U/L", "result_type": "-", "reference_range": "Children : 47 - 406 (Age and gender dependent)  Adults   : 80 - 306", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "PNPP-DGKC", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.494275", "updated_at": "2025-07-08T19:27:21.494277"}, {"id": 490, "test_name": "Glucose, 120 min", "test_code": "000238", "department": "BIOCHEMISTRY", "result_name": "Glucose, 120 min", "parameter_name": "Glucose, 120 min", "unit": "mg/dL", "result_type": "-", "reference_range": "Less than 155", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Colorimetric : GOD-POD", "specimen_type": "Fluoride", "container": "<PERSON>", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.494379", "updated_at": "2025-07-08T19:27:21.494381"}, {"id": 491, "test_name": "Glucose, 150 min", "test_code": "000239", "department": "BIOCHEMISTRY", "result_name": "Glucose, 150 min", "parameter_name": "Glucose, 150 min", "unit": null, "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Colorimetric : GOD-POD", "specimen_type": "Fluoride", "container": "<PERSON>", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.494534", "updated_at": "2025-07-08T19:27:21.494537"}, {"id": 492, "test_name": "Glucose, 180 min", "test_code": "000240", "department": "BIOCHEMISTRY", "result_name": "Glucose, 180 min", "parameter_name": "Glucose, 180 min", "unit": "mg/dL", "result_type": "-", "reference_range": "Less Than 140", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Colorimetric : GOD-POD", "specimen_type": "Fluoride", "container": "<PERSON>", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.494716", "updated_at": "2025-07-08T19:27:21.494719"}, {"id": 493, "test_name": "Glucose, 30 min", "test_code": "000235", "department": "BIOCHEMISTRY", "result_name": "Glucose, 30 min", "parameter_name": "Glucose, 30 min", "unit": "mg/dL", "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Colorimetric : GOD-POD", "specimen_type": "Fluoride", "container": "<PERSON>", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.494819", "updated_at": "2025-07-08T19:27:21.494821"}, {"id": 494, "test_name": "Glucose, 60 min", "test_code": "000236", "department": "BIOCHEMISTRY", "result_name": "Glucose, 60 min", "parameter_name": "Glucose, 60 min", "unit": "mg/dL", "result_type": "-", "reference_range": "Less than 180", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Colorimetric : GOD-POD", "specimen_type": "Fluoride", "container": "<PERSON>", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.494921", "updated_at": "2025-07-08T19:27:21.494924"}, {"id": 495, "test_name": "Glucose, 90 min", "test_code": "000237", "department": "BIOCHEMISTRY", "result_name": "Glucose, 90 min", "parameter_name": "Glucose, 90 min", "unit": null, "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Colorimetric : GOD-POD", "specimen_type": "Fluoride", "container": "<PERSON>", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.495023", "updated_at": "2025-07-08T19:27:21.495026"}, {"id": 496, "test_name": "Glucose, Ascitic Fluid", "test_code": "000152", "department": "BIOCHEMISTRY", "result_name": "Glucose, Ascitic Fluid", "parameter_name": "Glucose, Ascitic Fluid", "unit": "mg/dL", "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Colorimetric : GOD-POD", "specimen_type": "Ascitic fluid", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.495123", "updated_at": "2025-07-08T19:27:21.495126"}, {"id": 497, "test_name": "Glucose, Body fluids", "test_code": "000164", "department": "BIOCHEMISTRY", "result_name": "Glucose, Body fluids", "parameter_name": "Glucose, Body fluids", "unit": "mg/dL", "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Colorimetric : GOD-POD", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.495308", "updated_at": "2025-07-08T19:27:21.495324"}, {"id": 498, "test_name": "Glucose, CSF", "test_code": "000162", "department": "BIOCHEMISTRY", "result_name": "Glucose, CSF", "parameter_name": "Glucose, CSF", "unit": "mg/dL", "result_type": "-", "reference_range": "Children : 60-80  Adult    : 40-70", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Hexokinase", "specimen_type": "Cerebrospinal fluid", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.495457", "updated_at": "2025-07-08T19:27:21.495460"}, {"id": 499, "test_name": "Glucose, Fasting", "test_code": "000075", "department": "BIOCHEMISTRY", "result_name": "Glucose, Fasting", "parameter_name": "Glucose, Fasting", "unit": "mg/dL", "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Colorimetric : GOD-POD", "specimen_type": "Fluoride", "container": "<PERSON>", "instructions": "Samples to be collected between 10 to 12 hrs of Fasting in Fluoride Tube", "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.495559", "updated_at": "2025-07-08T19:27:21.495562"}, {"id": 500, "test_name": "Glucose, Pleural Fluid", "test_code": "000158", "department": "BIOCHEMISTRY", "result_name": "Glucose, Pleural Fluid", "parameter_name": "Glucose, Pleural Fluid", "unit": "mg/dL", "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Colorimetric : GOD-PAP.", "specimen_type": "Pleural Fluid", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.495659", "updated_at": "2025-07-08T19:27:21.495661"}, {"id": 501, "test_name": "Glu<PERSON><PERSON>, Post-Dinner", "test_code": "001665", "department": "BIOCHEMISTRY", "result_name": "Glu<PERSON><PERSON>, Post-Dinner", "parameter_name": "Glu<PERSON><PERSON>, Post-Dinner", "unit": "mg/dL", "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Colorimetric : GOD-POD", "specimen_type": "Fluoride", "container": "<PERSON>", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.495759", "updated_at": "2025-07-08T19:27:21.495762"}, {"id": 502, "test_name": "Glucose, Post-Lunch", "test_code": "001664", "department": "BIOCHEMISTRY", "result_name": "Glucose, Post-Lunch", "parameter_name": "Glucose, Post-Lunch", "unit": "mg/dL", "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Colorimetric : GOD-POD", "specimen_type": "Fluoride", "container": "<PERSON>", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.495861", "updated_at": "2025-07-08T19:27:21.495863"}, {"id": 503, "test_name": "<PERSON>lu<PERSON>e, Post-prandial", "test_code": "000123", "department": "BIOCHEMISTRY", "result_name": "<PERSON>lu<PERSON>e, Post-prandial", "parameter_name": "<PERSON>lu<PERSON>e, Post-prandial", "unit": "mg/dL", "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Colorimetric : GOD-POD", "specimen_type": "Fluoride", "container": "<PERSON>", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.496078", "updated_at": "2025-07-08T19:27:21.496081"}, {"id": 504, "test_name": "Glucose, Pre Dinner", "test_code": "001666", "department": "BIOCHEMISTRY", "result_name": "Glucose, Pre Dinner", "parameter_name": "Glucose, Pre Dinner", "unit": "mg/dL", "result_type": "-", "reference_range": null, "critical_low": 50.0, "critical_high": 300.0, "decimal_places": 1, "method": "Colorimetric : GOD - POD", "specimen_type": "FLURIDE PLASMA", "container": "<PERSON>", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.496194", "updated_at": "2025-07-08T19:27:21.496197"}, {"id": 505, "test_name": "Glucose, Pre-Lunch", "test_code": "001675", "department": "BIOCHEMISTRY", "result_name": "Glucose, Pre-Lunch", "parameter_name": "Glucose, Pre-Lunch", "unit": "mg/dL", "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Colorimetric : GOD-POD", "specimen_type": "FLURIDE PLASMA", "container": "<PERSON>", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.496296", "updated_at": "2025-07-08T19:27:21.496299"}, {"id": 506, "test_name": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>", "test_code": "000241", "department": "BIOCHEMISTRY", "result_name": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>", "parameter_name": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>", "unit": "mg/dL", "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "Colorimetric : GOD-POD", "specimen_type": "Fluoride", "container": "<PERSON>", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.496399", "updated_at": "2025-07-08T19:27:21.496401"}, {"id": 507, "test_name": "Glucose,Synovial Fluid", "test_code": "000267", "department": "BIOCHEMISTRY", "result_name": "Glucose,Synovial Fluid", "parameter_name": "Glucose,Synovial Fluid", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Colorimetric : GOD - POD", "specimen_type": "Synovial Fluid", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.496499", "updated_at": "2025-07-08T19:27:21.496502"}, {"id": 508, "test_name": "HAPTOGLOBIN", "test_code": "000263", "department": "BIOCHEMISTRY", "result_name": "HAPTOGLOBIN", "parameter_name": "HAPTOGLOBIN", "unit": "mg/dL", "result_type": "-", "reference_range": "30 - 200", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Immunoturbidimetry", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.496623", "updated_at": "2025-07-08T19:27:21.496626"}, {"id": 509, "test_name": "HbA1c", "test_code": "000086", "department": "BIOCHEMISTRY", "result_name": "HbA1c", "parameter_name": "HbA1c", "unit": null, "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "NEPHELOMETRY", "specimen_type": "EDTA BLOOD", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.496801", "updated_at": "2025-07-08T19:27:21.496803"}, {"id": 510, "test_name": "HDL/LDL-Direct Ratio", "test_code": "001533", "department": "BIOCHEMISTRY", "result_name": "HDL/LDL-Direct Ratio", "parameter_name": "HDL/LDL-Direct Ratio", "unit": "<PERSON><PERSON>", "result_type": "Calculated", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "Calculation", "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.496903", "updated_at": "2025-07-08T19:27:21.496905"}, {"id": 511, "test_name": "Hemosiderin", "test_code": "000294", "department": "BIOCHEMISTRY", "result_name": "Hemosiderin", "parameter_name": "Hemosiderin", "unit": null, "result_type": "No Unit / Ref. Value", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "URINE", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.497002", "updated_at": "2025-07-08T19:27:21.497004"}, {"id": 512, "test_name": "Homocysteine", "test_code": "000570", "department": "BIOCHEMISTRY", "result_name": "Homocysteine", "parameter_name": "Homocysteine", "unit": "umoI/L", "result_type": "-", "reference_range": "Folate supplemented diet Children <15 Yr: <8   Adult 15-65 Yr : <12  Elderly >65 Yr : <16   Pregnancy      : <8.0    No Folate supplementation Children <15 Yr: <10   Adult 15-65 Yr : <15   Elderly >65 Yr : <20 Pregnancy      : <10.0", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Edta Plasma/Serum", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.497103", "updated_at": "2025-07-08T19:27:21.497105"}, {"id": 513, "test_name": "hs- CRP", "test_code": "001134", "department": "BIOCHEMISTRY", "result_name": "hs- CRP", "parameter_name": "hs- CRP", "unit": "mg/dL", "result_type": "Numeric", "reference_range": "< 2.87", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "NEPHELOMETRY", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.497205", "updated_at": "2025-07-08T19:27:21.497207"}, {"id": 514, "test_name": "IA2 –INSULIN", "test_code": "001523", "department": "BIOCHEMISTRY", "result_name": "IA2 –INSULIN", "parameter_name": "IA2 –INSULIN", "unit": "U/mL", "result_type": "Numeric", "reference_range": "Negative:<28.0 Positive:>28.0", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "E.I.A", "specimen_type": "SERUM", "container": "Serum Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.497353", "updated_at": "2025-07-08T19:27:21.497356"}, {"id": 515, "test_name": "INHIBIN B", "test_code": "000265", "department": "BIOCHEMISTRY", "result_name": "INHIBIN B", "parameter_name": "INHIBIN B", "unit": "pg/ml", "result_type": "Pick List", "reference_range": "Below 6 years   : Less than 73 pg/ml  6 to 9 years    : Less than 129  10 years        : Less than 103  11 years        : 20 - 186  12 - 18 years   : 14 - 362  Early Follicular: Less than 261  Late Follicular : Less than 286  Pre Ovulatory   : Less than 189  Mid Luteal      : Less than 164  End Luteal      : Less than 107  Menopause       : Less than 7", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "EIA", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.497526", "updated_at": "2025-07-08T19:27:21.497529"}, {"id": 516, "test_name": "IRON", "test_code": "000090", "department": "BIOCHEMISTRY", "result_name": "IRON", "parameter_name": "IRON", "unit": "ug/dL", "result_type": "Numeric", "reference_range": "Adults : 50 - 120 (Ferro<PERSON>ine)", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": null, "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.497628", "updated_at": "2025-07-08T19:27:21.497631"}, {"id": 517, "test_name": "Ketone (D3 Hydroxybutyrate)", "test_code": "000283", "department": "BIOCHEMISTRY", "result_name": "Ketone (D3 Hydroxybutyrate)", "parameter_name": "Ketone (D3 Hydroxybutyrate)", "unit": "mg/dL", "result_type": "Pick List", "reference_range": "0.21 - 2.81", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Enzymatic", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.497729", "updated_at": "2025-07-08T19:27:21.497731"}, {"id": 518, "test_name": "Lactate", "test_code": "000093", "department": "BIOCHEMISTRY", "result_name": "Lactate", "parameter_name": "Lactate", "unit": "mmoL/L", "result_type": "-", "reference_range": "0.5 - 2.2", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": null, "specimen_type": "FLURIDE PLASMA", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.497829", "updated_at": "2025-07-08T19:27:21.497832"}, {"id": 519, "test_name": "Lactate Dehydrogenase (LDH)", "test_code": "000094", "department": "BIOCHEMISTRY", "result_name": "Lactate Dehydrogenase (LDH)", "parameter_name": "Lactate Dehydrogenase (LDH)", "unit": "U/L", "result_type": "-", "reference_range": "Females  : 135 - 214  Males    : 135 - 225  Children : 120 - 300   Newborns : 225 - 600", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Colorimetric :   Lactate -  Pyruvate", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.497930", "updated_at": "2025-07-08T19:27:21.497933"}, {"id": 520, "test_name": "Lactate Dehydrogenase (LDH), Ascitic Fluid", "test_code": "000096", "department": "BIOCHEMISTRY", "result_name": "Lactate Dehydrogenase (LDH), Ascitic Fluid", "parameter_name": "Lactate Dehydrogenase (LDH), Ascitic Fluid", "unit": "U/L", "result_type": "Numeric", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IFCC", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.498089", "updated_at": "2025-07-08T19:27:21.498092"}, {"id": 521, "test_name": "Lactate Dehydrogenase (LDH), Pleural fluid", "test_code": "000098", "department": "BIOCHEMISTRY", "result_name": "Lactate Dehydrogenase (LDH), Pleural fluid", "parameter_name": "Lactate Dehydrogenase (LDH), Pleural fluid", "unit": "U/l", "result_type": "Pick List", "reference_range": "114-220 IU/L", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IFCC", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.498217", "updated_at": "2025-07-08T19:27:21.498220"}, {"id": 522, "test_name": "Lactate, CSF", "test_code": "000092", "department": "BIOCHEMISTRY", "result_name": "Lactate, CSF", "parameter_name": "Lactate, CSF", "unit": "mmoL/L", "result_type": "Numeric", "reference_range": "Adult       : 1.1 - 2.4  Neonates    : 1.1 - 6.7 3 to 10 days: 1.1 - 4.4 >10 days    : 1.1 - 2.8", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "LOD - POD", "specimen_type": "Cerebrospinal fluid", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.498319", "updated_at": "2025-07-08T19:27:21.498321"}, {"id": 523, "test_name": "LDH - CSF", "test_code": "000095", "department": "BIOCHEMISTRY", "result_name": "LDH - CSF", "parameter_name": "LDH - CSF", "unit": "U/L", "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IFCC", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.498422", "updated_at": "2025-07-08T19:27:21.498424"}, {"id": 524, "test_name": "LDH -Peritoneal Fluid", "test_code": "000097", "department": "BIOCHEMISTRY", "result_name": "LDH -Peritoneal Fluid", "parameter_name": "LDH -Peritoneal Fluid", "unit": "U/L", "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IFCC", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.498519", "updated_at": "2025-07-08T19:27:21.498522"}, {"id": 525, "test_name": "LDL-Direct/HDL  Ratio", "test_code": "001409", "department": "BIOCHEMISTRY", "result_name": "LDL-Direct/HDL  Ratio", "parameter_name": "LDL-Direct/HDL  Ratio", "unit": "<PERSON><PERSON>", "result_type": "Calculated", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Calculation", "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.498619", "updated_at": "2025-07-08T19:27:21.498621"}, {"id": 526, "test_name": "LDL/HDL Ratio", "test_code": "001436", "department": "BIOCHEMISTRY", "result_name": "LDL/HDL Ratio", "parameter_name": "LDL/HDL Ratio", "unit": "<PERSON><PERSON>", "result_type": "Calculated", "reference_range": "Castelli's Risk Index -II Ideal : <2.0 Good: 2.0-5.0 High: >=5", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Calculation", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.498792", "updated_at": "2025-07-08T19:27:21.498795"}, {"id": 527, "test_name": "Lipase", "test_code": "000101", "department": "BIOCHEMISTRY", "result_name": "Lipase", "parameter_name": "Lipase", "unit": "U/L", "result_type": "Numeric", "reference_range": "ADULT : 13 - 60", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Methyl Re<PERSON>ufin", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.498906", "updated_at": "2025-07-08T19:27:21.498909"}, {"id": 528, "test_name": "LIPOPROTEIN (a)", "test_code": "000103", "department": "BIOCHEMISTRY", "result_name": "LIPOPROTEIN (a)", "parameter_name": "LIPOPROTEIN (a)", "unit": "nmol/L", "result_type": "Numeric", "reference_range": "Less than 75.0", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "NEPHELOMETRY", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.499007", "updated_at": "2025-07-08T19:27:21.499009"}, {"id": 529, "test_name": "Lithium", "test_code": "000105", "department": "BIOCHEMISTRY", "result_name": "Lithium", "parameter_name": "Lithium", "unit": "mmoL/L", "result_type": "Numeric", "reference_range": "0.6 - 1.2", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "Ion Selective Electrode", "specimen_type": "Serum", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.499107", "updated_at": "2025-07-08T19:27:21.499110"}, {"id": 530, "test_name": "LP-PLA2 (LIPOPROTEIN ASSOCIATED PHOSPHOLIPASE A2)", "test_code": "001413", "department": "BIOCHEMISTRY", "result_name": "LP-PLA2 (LIPOPROTEIN ASSOCIATED PHOSPHOLIPASE A2)", "parameter_name": "LP-PLA2 (LIPOPROTEIN ASSOCIATED PHOSPHOLIPASE A2)", "unit": "U/L", "result_type": "Numeric", "reference_range": "Low Risk  : < 275  High Risk : > 275", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Enzymatic", "specimen_type": "SERUM", "container": "<PERSON><PERSON> Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.499208", "updated_at": "2025-07-08T19:27:21.499211"}, {"id": 531, "test_name": "Magnesium", "test_code": "000106", "department": "BIOCHEMISTRY", "result_name": "Magnesium", "parameter_name": "Magnesium", "unit": "mg/dL", "result_type": "Numeric", "reference_range": "1.3 - 2.5", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Xylidyl Blue", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.499309", "updated_at": "2025-07-08T19:27:21.499313"}, {"id": 532, "test_name": "Magnesium, Urine", "test_code": "001316", "department": "BIOCHEMISTRY", "result_name": "Magnesium, Urine", "parameter_name": "Magnesium, Urine", "unit": "mg/dL", "result_type": "Numeric", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Biochemical", "specimen_type": "URINE", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.499496", "updated_at": "2025-07-08T19:27:21.499498"}, {"id": 533, "test_name": "METANEPHRINE (SPOT)", "test_code": "000270", "department": "BIOCHEMISTRY", "result_name": "METANEPHRINE (SPOT)", "parameter_name": "METANEPHRINE (SPOT)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.499593", "updated_at": "2025-07-08T19:27:21.499595"}, {"id": 534, "test_name": "Met<PERSON><PERSON><PERSON>, 24 Hrs", "test_code": "000249", "department": "BIOCHEMISTRY", "result_name": "Met<PERSON><PERSON><PERSON>, 24 Hrs", "parameter_name": "Met<PERSON><PERSON><PERSON>, 24 Hrs", "unit": null, "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "24 H<PERSON> <PERSON><PERSON>", "container": "<PERSON><PERSON>tainer", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.499692", "updated_at": "2025-07-08T19:27:21.499694"}, {"id": 535, "test_name": "Metanephrine-free, plasma", "test_code": "000289", "department": "BIOCHEMISTRY", "result_name": "Metanephrine-free, plasma", "parameter_name": "Metanephrine-free, plasma", "unit": "pg/ml", "result_type": "-", "reference_range": "Less than 65", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "E.L.I.S.A", "specimen_type": "EDTA PLASMA", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.499793", "updated_at": "2025-07-08T19:27:21.499795"}, {"id": 536, "test_name": "Methyl Malonic Acid", "test_code": "001321", "department": "BIOCHEMISTRY", "result_name": "Methyl Malonic Acid", "parameter_name": "Methyl Malonic Acid", "unit": null, "result_type": "Pick List", "reference_range": "Negative", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Biochemical", "specimen_type": "URINE", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.499891", "updated_at": "2025-07-08T19:27:21.499894"}, {"id": 537, "test_name": "METHYLMALONIC ACID –QUANTITATIVE", "test_code": "001525", "department": "BIOCHEMISTRY", "result_name": "METHYLMALONIC ACID –QUANTITATIVE", "parameter_name": "METHYLMALONIC ACID –QUANTITATIVE", "unit": "mg/g creatinine", "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "LC-MS/MS", "specimen_type": "SERUM/URINE", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.500011", "updated_at": "2025-07-08T19:27:21.500062"}, {"id": 538, "test_name": "Microalbumin", "test_code": "000107", "department": "BIOCHEMISTRY", "result_name": "Microalbumin", "parameter_name": "Microalbumin", "unit": null, "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "NEPHELOMETRY", "specimen_type": "<PERSON><PERSON>", "container": "<PERSON><PERSON>tainer", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.500217", "updated_at": "2025-07-08T19:27:21.500220"}, {"id": 539, "test_name": "Microalbumin, Urine 24Hr", "test_code": "000012", "department": "BIOCHEMISTRY", "result_name": "Microalbumin, Urine 24Hr", "parameter_name": "Microalbumin, Urine 24Hr", "unit": null, "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "<PERSON><PERSON>", "specimen_type": null, "container": "<PERSON><PERSON><PERSON>er", "instructions": "sample to be collected in Lab container(10 % Thymol 5ml), mention total volume", "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.500321", "updated_at": "2025-07-08T19:27:21.500324"}, {"id": 540, "test_name": "Microalbumin/<PERSON> , Urine", "test_code": "000177", "department": "BIOCHEMISTRY", "result_name": "Microalbumin/<PERSON> , Urine", "parameter_name": "Microalbumin/<PERSON> , Urine", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "URINE", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.500422", "updated_at": "2025-07-08T19:27:21.500424"}, {"id": 541, "test_name": "MUCOPOLYSACCHARIDES (Glycosaminoglycan (GAG))", "test_code": "000194", "department": "BIOCHEMISTRY", "result_name": "MUCOPOLYSACCHARIDES (Glycosaminoglycan (GAG))", "parameter_name": "MUCOPOLYSACCHARIDES (Glycosaminoglycan (GAG))", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Biochemical", "specimen_type": "URINE", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.500521", "updated_at": "2025-07-08T19:27:21.500523"}, {"id": 542, "test_name": "<PERSON>og<PERSON>bin, Urine", "test_code": "000279", "department": "BIOCHEMISTRY", "result_name": "<PERSON>og<PERSON>bin, Urine", "parameter_name": "<PERSON>og<PERSON>bin, Urine", "unit": "ug/L", "result_type": "Pick List", "reference_range": "0 - 1000", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ECLIA", "specimen_type": "URINE", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.500640", "updated_at": "2025-07-08T19:27:21.500655"}, {"id": 543, "test_name": "<PERSON><PERSON>", "test_code": "001333", "department": "BIOCHEMISTRY", "result_name": "<PERSON><PERSON>", "parameter_name": "<PERSON><PERSON>", "unit": "ug/L", "result_type": "-", "reference_range": "0.14 - 1.00", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "ICPMS", "specimen_type": "Edta Plasma/Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.500808", "updated_at": "2025-07-08T19:27:21.500811"}, {"id": 544, "test_name": "<PERSON><PERSON> (Cotinine) Metaboli<PERSON>, Card", "test_code": "001547", "department": "BIOCHEMISTRY", "result_name": "<PERSON><PERSON> (Cotinine) Metaboli<PERSON>, Card", "parameter_name": "<PERSON><PERSON> (Cotinine) Metaboli<PERSON>, Card", "unit": null, "result_type": "Pick List", "reference_range": "Negative", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Immuno Chromatography", "specimen_type": "URINE", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.500910", "updated_at": "2025-07-08T19:27:21.500913"}, {"id": 545, "test_name": "Nicotine Metabolite", "test_code": "000264", "department": "BIOCHEMISTRY", "result_name": "Nicotine Metabolite", "parameter_name": "Nicotine Metabolite", "unit": "ng/ml", "result_type": "-", "reference_range": "SERUM Smokers     : Above 25  Non-smokers : Below 25   URINE Negative    : Below 500 Positive    : 500 & Above", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.501008", "updated_at": "2025-07-08T19:27:21.501011"}, {"id": 546, "test_name": "Non-Invasive Prenatal Testing (NIPT)", "test_code": "001663", "department": "BIOCHEMISTRY", "result_name": "Non-Invasive Prenatal Testing (NIPT)", "parameter_name": "Non-Invasive Prenatal Testing (NIPT)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.501104", "updated_at": "2025-07-08T19:27:21.501107"}, {"id": 547, "test_name": "Nor-Metanephrine, Urine 24 Hrs", "test_code": "001332", "department": "BIOCHEMISTRY", "result_name": "Nor-Metanephrine, Urine 24 Hrs", "parameter_name": "Nor-Metanephrine, Urine 24 Hrs", "unit": null, "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "24 H<PERSON> <PERSON><PERSON>", "container": "<PERSON><PERSON>tainer", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.501202", "updated_at": "2025-07-08T19:27:21.501205"}, {"id": 548, "test_name": "Oligoclonal band,CSF", "test_code": "001355", "department": "BIOCHEMISTRY", "result_name": "Oligoclonal band,CSF", "parameter_name": "Oligoclonal band,CSF", "unit": null, "result_type": "Template", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.501346", "updated_at": "2025-07-08T19:27:21.501348"}, {"id": 549, "test_name": "Opiates (Morphine)", "test_code": "000187", "department": "BIOCHEMISTRY", "result_name": "Opiates (Morphine)", "parameter_name": "Opiates (Morphine)", "unit": null, "result_type": "Pick List", "reference_range": "Negative", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IC", "specimen_type": "URINE", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.501488", "updated_at": "2025-07-08T19:27:21.501490"}, {"id": 550, "test_name": "OSMOLALITY (SERUM)", "test_code": "000296", "department": "BIOCHEMISTRY", "result_name": "OSMOLALITY (SERUM)", "parameter_name": "OSMOLALITY (SERUM)", "unit": "mOsm/kg", "result_type": "-", "reference_range": "280 - 305.", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Calculated", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.501588", "updated_at": "2025-07-08T19:27:21.501591"}, {"id": 551, "test_name": "OSMOLALITY (Urine)", "test_code": "000588", "department": "BIOCHEMISTRY", "result_name": "OSMOLALITY (Urine)", "parameter_name": "OSMOLALITY (Urine)", "unit": "mOsm/kg", "result_type": "-", "reference_range": "50 - 1400.", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.501687", "updated_at": "2025-07-08T19:27:21.501690"}, {"id": 552, "test_name": "PhenylAlanine Screen", "test_code": "000276", "department": "BIOCHEMISTRY", "result_name": "PhenylAlanine Screen", "parameter_name": "PhenylAlanine Screen", "unit": null, "result_type": "-", "reference_range": "NOT DETECTED", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.501785", "updated_at": "2025-07-08T19:27:21.501788"}, {"id": 553, "test_name": "Phosphorous", "test_code": "000209", "department": "BIOCHEMISTRY", "result_name": "Phosphorous", "parameter_name": "Phosphorous", "unit": "mg/dL", "result_type": "-", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "Phosphomolybdate complex", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.501885", "updated_at": "2025-07-08T19:27:21.501888"}, {"id": 554, "test_name": "PLASMA ACETONE", "test_code": "000147", "department": "BIOCHEMISTRY", "result_name": "PLASMA ACETONE", "parameter_name": "PLASMA ACETONE", "unit": null, "result_type": "Pick List", "reference_range": "Not Present", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Enzymatic", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.504057", "updated_at": "2025-07-08T19:27:21.504071"}, {"id": 555, "test_name": "PLEURAL FLUID AMYLASE", "test_code": "000120", "department": "BIOCHEMISTRY", "result_name": "PLEURAL FLUID AMYLASE", "parameter_name": "PLEURAL FLUID AMYLASE", "unit": "U/L", "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Enzymatic", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.504472", "updated_at": "2025-07-08T19:27:21.504481"}, {"id": 556, "test_name": "<PERSON>rphop<PERSON><PERSON><PERSON>, <PERSON><PERSON> 24 Hrs,", "test_code": "001412", "department": "BIOCHEMISTRY", "result_name": "<PERSON>rphop<PERSON><PERSON><PERSON>, <PERSON><PERSON> 24 Hrs,", "parameter_name": "<PERSON>rphop<PERSON><PERSON><PERSON>, <PERSON><PERSON> 24 Hrs,", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "URINE", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.504832", "updated_at": "2025-07-08T19:27:21.504838"}, {"id": 557, "test_name": "Potassium, Urine", "test_code": "000121", "department": "BIOCHEMISTRY", "result_name": "Potassium, Urine", "parameter_name": "Potassium, Urine", "unit": "mmol/L", "result_type": "Pick List", "reference_range": "Adult (< 40 Yr) Male  : 11 - 80 Female: 17 - 145  Adult (= 40 Yr) Male  : 17 - 99 Female: 22 - 164", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Ion Selective Electrode", "specimen_type": "<PERSON><PERSON>", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.505012", "updated_at": "2025-07-08T19:27:21.505015"}, {"id": 558, "test_name": "Potassium, Urine 24Hr", "test_code": "000122", "department": "BIOCHEMISTRY", "result_name": "Potassium, Urine 24Hr", "parameter_name": "Potassium, Urine 24Hr", "unit": null, "result_type": null, "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Ion Selective Electrode", "specimen_type": "<PERSON><PERSON>", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.505158", "updated_at": "2025-07-08T19:27:21.505161"}, {"id": 559, "test_name": "Potassium", "test_code": "000206", "department": "BIOCHEMISTRY", "result_name": "Potassium", "parameter_name": "Potassium", "unit": "mmol/L", "result_type": null, "reference_range": "New Born : 3.7 - 5.9 Infant   : 4.1 - 5.3 Child    : 3.4 - 4.7 Adults   : 3.5 - 5.1", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Ion Selective Electrode", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.505300", "updated_at": "2025-07-08T19:27:21.505303"}, {"id": 560, "test_name": "PROSTATIC ACID PHOSPHATASE", "test_code": "000211", "department": "BIOCHEMISTRY", "result_name": "PROSTATIC ACID PHOSPHATASE", "parameter_name": "PROSTATIC ACID PHOSPHATASE", "unit": "U/L", "result_type": null, "reference_range": "Upto 3.0", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Enzymatic", "specimen_type": "Serum", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.505438", "updated_at": "2025-07-08T19:27:21.505441"}, {"id": 561, "test_name": "Protein Creatinine <PERSON>, Urine", "test_code": "000200", "department": "BIOCHEMISTRY", "result_name": "Protein Creatinine <PERSON>, Urine", "parameter_name": "Protein Creatinine <PERSON>, Urine", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "<PERSON><PERSON>", "container": "<PERSON><PERSON>tainer", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.505574", "updated_at": "2025-07-08T19:27:21.505577"}, {"id": 562, "test_name": "<PERSON><PERSON>, <PERSON>citic Fluid", "test_code": "000126", "department": "BIOCHEMISTRY", "result_name": "<PERSON><PERSON>, <PERSON>citic Fluid", "parameter_name": "<PERSON><PERSON>, <PERSON>citic Fluid", "unit": "g/dL", "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Colorimetric-Biuret", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.505709", "updated_at": "2025-07-08T19:27:21.505712"}, {"id": 563, "test_name": "Protein,  Pericardial fluid", "test_code": "000125", "department": "BIOCHEMISTRY", "result_name": "Protein,  Pericardial fluid", "parameter_name": "Protein,  Pericardial fluid", "unit": "g/dL", "result_type": null, "reference_range": "Transudate : <2.5  Exudate    : >3.0", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Colorimetric-Biuret", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.505842", "updated_at": "2025-07-08T19:27:21.505846"}, {"id": 564, "test_name": "<PERSON>tein, Body fluids", "test_code": "000128", "department": "BIOCHEMISTRY", "result_name": "<PERSON>tein, Body fluids", "parameter_name": "<PERSON>tein, Body fluids", "unit": "g/dL", "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Colorimetric-Biuret", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.505971", "updated_at": "2025-07-08T19:27:21.505974"}, {"id": 565, "test_name": "<PERSON><PERSON>, CSF", "test_code": "000136", "department": "BIOCHEMISTRY", "result_name": "<PERSON><PERSON>, CSF", "parameter_name": "<PERSON><PERSON>, CSF", "unit": "mg/dL", "result_type": null, "reference_range": "15- 45", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Colorimetric : Pyrogallol red", "specimen_type": "Cerebrospinal fluid", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.506102", "updated_at": "2025-07-08T19:27:21.506105"}, {"id": 566, "test_name": "Protein, Peritoneal Fluid", "test_code": "000139", "department": "BIOCHEMISTRY", "result_name": "Protein, Peritoneal Fluid", "parameter_name": "Protein, Peritoneal Fluid", "unit": "g/dL", "result_type": null, "reference_range": "Transudate : 1.0 - 3.0  Exudate    : >3.0", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Colorimetric-Biuret", "specimen_type": "Peritoneal fluid", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.506230", "updated_at": "2025-07-08T19:27:21.506233"}, {"id": 567, "test_name": "<PERSON><PERSON>, Pleural Fluid", "test_code": "000130", "department": "BIOCHEMISTRY", "result_name": "<PERSON><PERSON>, Pleural Fluid", "parameter_name": "<PERSON><PERSON>, Pleural Fluid", "unit": "g/dL", "result_type": "Numeric", "reference_range": "Transudate : <2.5 Exudate    : >3.0", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "Colorimetric-Biuret", "specimen_type": "<PERSON><PERSON><PERSON>", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.506363", "updated_at": "2025-07-08T19:27:21.506366"}, {"id": 568, "test_name": "<PERSON><PERSON>, Urine", "test_code": "000199", "department": "BIOCHEMISTRY", "result_name": "<PERSON><PERSON>, Urine", "parameter_name": "<PERSON><PERSON>, Urine", "unit": "mg/dL", "result_type": "Numeric", "reference_range": "Less than 15.0", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Colorimetric : Pyrogallol red", "specimen_type": "<PERSON><PERSON>", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.506494", "updated_at": "2025-07-08T19:27:21.506497"}, {"id": 569, "test_name": "<PERSON><PERSON>, <PERSON>rine 24Hr", "test_code": "000011", "department": "BIOCHEMISTRY", "result_name": "<PERSON><PERSON>, <PERSON>rine 24Hr", "parameter_name": "<PERSON><PERSON>, <PERSON>rine 24Hr", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "<PERSON><PERSON>", "container": "<PERSON><PERSON><PERSON>er", "instructions": "sample to be collected in Lab container(10% Thymol 5ml), mention total volume", "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.506623", "updated_at": "2025-07-08T19:27:21.506626"}, {"id": 570, "test_name": "<PERSON><PERSON>,Synovial Fluid", "test_code": "000268", "department": "BIOCHEMISTRY", "result_name": "<PERSON><PERSON>,Synovial Fluid", "parameter_name": "<PERSON><PERSON>,Synovial Fluid", "unit": "g/dL", "result_type": "Pick List", "reference_range": "Transudate : <2.5 Exudate    : >3.0", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Colorimetric-Biuret", "specimen_type": "Synovial Fluid", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.506749", "updated_at": "2025-07-08T19:27:21.506753"}, {"id": 571, "test_name": "PYRUVATE", "test_code": "001522", "department": "BIOCHEMISTRY", "result_name": "PYRUVATE", "parameter_name": "PYRUVATE", "unit": "mg/dL", "result_type": "Pick List", "reference_range": "0.0 – 0.7", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Enzymatic", "specimen_type": "SERUM", "container": "Serum Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.506876", "updated_at": "2025-07-08T19:27:21.506879"}, {"id": 572, "test_name": "Sodium, Urine", "test_code": "000151", "department": "BIOCHEMISTRY", "result_name": "Sodium, Urine", "parameter_name": "Sodium, Urine", "unit": "mmol/L", "result_type": "Pick List", "reference_range": "Adult (< 40 Yr) Male  : 25 - 301 Female: 15 - 267  Adult (>= 40 Yr) Male  : 18 - 214 Female: 15 - 237", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Ion Selective Electrode", "specimen_type": "<PERSON><PERSON>", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.507006", "updated_at": "2025-07-08T19:27:21.507009"}, {"id": 573, "test_name": "Sodium, Urine 24Hr", "test_code": "000150", "department": "BIOCHEMISTRY", "result_name": "Sodium, Urine 24Hr", "parameter_name": "Sodium, Urine 24Hr", "unit": null, "result_type": null, "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "<PERSON><PERSON>", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.507134", "updated_at": "2025-07-08T19:27:21.507137"}, {"id": 574, "test_name": "Sodium", "test_code": "000205", "department": "BIOCHEMISTRY", "result_name": "Sodium", "parameter_name": "Sodium", "unit": "mmol/L", "result_type": null, "reference_range": "New Born : 133 - 146 Infant   : 139 - 146 Child    : 138 - 145 Adult    : 136 - 145 >90 years: 132 - 146", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Ion Selective Electrode", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.507261", "updated_at": "2025-07-08T19:27:21.507264"}, {"id": 575, "test_name": "STONE ANALYSIS", "test_code": "000291", "department": "BIOCHEMISTRY", "result_name": "STONE ANALYSIS", "parameter_name": "STONE ANALYSIS", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "FTIR", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.507387", "updated_at": "2025-07-08T19:27:21.507391"}, {"id": 576, "test_name": "SUCROSE LYSIS TEST(PNH SCREENING TEST)", "test_code": "000243", "department": "BIOCHEMISTRY", "result_name": "SUCROSE LYSIS TEST(PNH SCREENING TEST)", "parameter_name": "SUCROSE LYSIS TEST(PNH SCREENING TEST)", "unit": null, "result_type": "Pick List", "reference_range": "NEGATIVE", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "FTIR", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.507513", "updated_at": "2025-07-08T19:27:21.507516"}, {"id": 577, "test_name": "Total Iron Binding Capacity", "test_code": "000166", "department": "BIOCHEMISTRY", "result_name": "Total Iron Binding Capacity", "parameter_name": "Total Iron Binding Capacity", "unit": null, "result_type": null, "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.507638", "updated_at": "2025-07-08T19:27:21.507642"}, {"id": 578, "test_name": "Total Protein.", "test_code": "000167", "department": "BIOCHEMISTRY", "result_name": "Total Protein.", "parameter_name": "Total Protein.", "unit": "g/dL", "result_type": null, "reference_range": "Adult : 6.6 - 8.7", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "Colorimetric-Biuret", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.507768", "updated_at": "2025-07-08T19:27:21.507772"}, {"id": 579, "test_name": "Toxic Elements - 22", "test_code": "001513", "department": "BIOCHEMISTRY", "result_name": "Toxic Elements - 22", "parameter_name": "Toxic Elements - 22", "unit": null, "result_type": "Template", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ICPMS", "specimen_type": "WHOLE BLOOD", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.507895", "updated_at": "2025-07-08T19:27:21.507899"}, {"id": 580, "test_name": "TPMT ENZYME ACTIVITY (Thiopurine Methyl Transferase)", "test_code": "001431", "department": "BIOCHEMISTRY", "result_name": "TPMT ENZYME ACTIVITY (Thiopurine Methyl Transferase)", "parameter_name": "TPMT ENZYME ACTIVITY (Thiopurine Methyl Transferase)", "unit": "units", "result_type": null, "reference_range": "<5.5    : Low Activity 5.5-15.5: Intermediate Activity >15.5   : High Activity", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "EIA", "specimen_type": "EDTA PLASMA", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.508021", "updated_at": "2025-07-08T19:27:21.508025"}, {"id": 581, "test_name": "Transferrin Saturation", "test_code": "000168", "department": "BIOCHEMISTRY", "result_name": "Transferrin Saturation", "parameter_name": "Transferrin Saturation", "unit": null, "result_type": null, "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.508149", "updated_at": "2025-07-08T19:27:21.508152"}, {"id": 582, "test_name": "Tricyclic Antidepressants (TCA)", "test_code": "001434", "department": "BIOCHEMISTRY", "result_name": "Tricyclic Antidepressants (TCA)", "parameter_name": "Tricyclic Antidepressants (TCA)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Immuno Chromatography", "specimen_type": "URINE", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.508273", "updated_at": "2025-07-08T19:27:21.508276"}, {"id": 583, "test_name": "Triglycerides", "test_code": "000169", "department": "BIOCHEMISTRY", "result_name": "Triglycerides", "parameter_name": "Triglycerides", "unit": "mg/dL", "result_type": null, "reference_range": null, "critical_low": 50.0, "critical_high": 700.0, "decimal_places": 1, "method": "Glycerol-3-phosphate oxidase-PAP", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.508402", "updated_at": "2025-07-08T19:27:21.508405"}, {"id": 584, "test_name": "Troponin I", "test_code": "000170", "department": "BIOCHEMISTRY", "result_name": "Troponin I", "parameter_name": "Troponin I", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Chromatographic Lateral Flow Immunoassay", "specimen_type": "Serum/Whole Blood", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.508530", "updated_at": "2025-07-08T19:27:21.508534"}, {"id": 585, "test_name": "Troponin T", "test_code": "000171", "department": "BIOCHEMISTRY", "result_name": "Troponin T", "parameter_name": "Troponin T", "unit": null, "result_type": "Pick List", "reference_range": "Negative (<0.1 ng/ml)", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "IC", "specimen_type": "EDTA", "container": "EDTA Container", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.508664", "updated_at": "2025-07-08T19:27:21.508667"}, {"id": 586, "test_name": "Troponin- I (high sensitive), Quantitative", "test_code": "001584", "department": "BIOCHEMISTRY", "result_name": "Troponin- I (high sensitive), Quantitative", "parameter_name": "Troponin- I (high sensitive), Quantitative", "unit": "pg/mL", "result_type": "Numeric", "reference_range": "Less than 26.20", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "CMIA", "specimen_type": "SERUM", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.508773", "updated_at": "2025-07-08T19:27:21.508776"}, {"id": 587, "test_name": "Troponin- T (high sensitive), Quantitative", "test_code": "000287", "department": "BIOCHEMISTRY", "result_name": "Troponin- T (high sensitive), Quantitative", "parameter_name": "Troponin- T (high sensitive), Quantitative", "unit": "pg/mL", "result_type": "Pick List", "reference_range": "<14.0 : Negative >14.0 : Positive", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "ECLIA", "specimen_type": "EDTA PLASMA", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.508880", "updated_at": "2025-07-08T19:27:21.508883"}, {"id": 588, "test_name": "Urea", "test_code": "000214", "department": "BIOCHEMISTRY", "result_name": "Urea", "parameter_name": "Urea", "unit": "mg/dL", "result_type": "Numeric", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "Urease/GLDH", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.508989", "updated_at": "2025-07-08T19:27:21.508992"}, {"id": 589, "test_name": "UREA CLEARANCE 24 HOUR URINE", "test_code": "000174", "department": "BIOCHEMISTRY", "result_name": "UREA CLEARANCE 24 HOUR URINE", "parameter_name": "UREA CLEARANCE 24 HOUR URINE", "unit": "ml/min", "result_type": "Numeric", "reference_range": "41 - 68", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Calculation", "specimen_type": "24 H<PERSON> <PERSON><PERSON>", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.509102", "updated_at": "2025-07-08T19:27:21.509105"}, {"id": 590, "test_name": "Urea, Urine", "test_code": "000173", "department": "BIOCHEMISTRY", "result_name": "Urea, Urine", "parameter_name": "Urea, Urine", "unit": "mg/dL", "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Enzymatic-Kinetic", "specimen_type": "<PERSON><PERSON>", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.509212", "updated_at": "2025-07-08T19:27:21.509215"}, {"id": 591, "test_name": "Urea, Urine 24Hr", "test_code": "000172", "department": "BIOCHEMISTRY", "result_name": "Urea, Urine 24Hr", "parameter_name": "Urea, Urine 24Hr", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "<PERSON><PERSON>", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.509320", "updated_at": "2025-07-08T19:27:21.509323"}, {"id": 592, "test_name": "<PERSON><PERSON>, <PERSON>rine 24Hr", "test_code": "000221", "department": "BIOCHEMISTRY", "result_name": "<PERSON><PERSON>, <PERSON>rine 24Hr", "parameter_name": "<PERSON><PERSON>, <PERSON>rine 24Hr", "unit": null, "result_type": null, "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.509428", "updated_at": "2025-07-08T19:27:21.509431"}, {"id": 593, "test_name": "Uric Acid.", "test_code": "000175", "department": "BIOCHEMISTRY", "result_name": "Uric Acid.", "parameter_name": "Uric Acid.", "unit": "mg/dL", "result_type": null, "reference_range": "Child  : 2.0 - 5.0 Adult Male   : 3.5 - 7.2 Female : 2.6 - 6.0", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "Uricase/peroxidase", "specimen_type": "Serum", "container": "PLAIN", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.509539", "updated_at": "2025-07-08T19:27:21.509541"}, {"id": 594, "test_name": "URICACID / CREATININE", "test_code": "000253", "department": "BIOCHEMISTRY", "result_name": "URICACID / CREATININE", "parameter_name": "URICACID / CREATININE", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.509645", "updated_at": "2025-07-08T19:27:21.509648"}, {"id": 595, "test_name": "Uricacid, Synovial fluid", "test_code": "000259", "department": "BIOCHEMISTRY", "result_name": "Uricacid, Synovial fluid", "parameter_name": "Uricacid, Synovial fluid", "unit": "mg/dL", "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Uricase/peroxidase", "specimen_type": "Synovial Fluid", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.509758", "updated_at": "2025-07-08T19:27:21.509761"}, {"id": 596, "test_name": "URINE  ALKAPTANURIA (Homogentisic Acid)", "test_code": "000202", "department": "BIOCHEMISTRY", "result_name": "URINE  ALKAPTANURIA (Homogentisic Acid)", "parameter_name": "URINE  ALKAPTANURIA (Homogentisic Acid)", "unit": null, "result_type": "Pick List", "reference_range": "NEGATIVE", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Biochemical", "specimen_type": "URINE", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.509869", "updated_at": "2025-07-08T19:27:21.509872"}, {"id": 597, "test_name": "URINE ALCOHOL", "test_code": "000227", "department": "BIOCHEMISTRY", "result_name": "URINE ALCOHOL", "parameter_name": "URINE ALCOHOL", "unit": "mg/dL", "result_type": "Pick List", "reference_range": "NOT DETECTABLE", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Enzymatic", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.509978", "updated_at": "2025-07-08T19:27:21.509981"}, {"id": 598, "test_name": "URINE CALCIUM CREATININE RATIO", "test_code": "000180", "department": "BIOCHEMISTRY", "result_name": "URINE CALCIUM CREATININE RATIO", "parameter_name": "URINE CALCIUM CREATININE RATIO", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.510085", "updated_at": "2025-07-08T19:27:21.510088"}, {"id": 599, "test_name": "URINE CHROMATOGRAPHY", "test_code": "000251", "department": "BIOCHEMISTRY", "result_name": "URINE CHROMATOGRAPHY", "parameter_name": "URINE CHROMATOGRAPHY", "unit": null, "result_type": "No Unit / Ref. Value", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "TLC", "specimen_type": "URINE", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.510193", "updated_at": "2025-07-08T19:27:21.510196"}, {"id": 600, "test_name": "<PERSON><PERSON> (Nicotine) Card", "test_code": "001730", "department": "BIOCHEMISTRY", "result_name": "<PERSON><PERSON> (Nicotine) Card", "parameter_name": "<PERSON><PERSON> (Nicotine) Card", "unit": null, "result_type": "Pick List", "reference_range": "Negative", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "URINE", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.510301", "updated_at": "2025-07-08T19:27:21.510304"}, {"id": 601, "test_name": "URINE METABOLIC SCREENING", "test_code": "001678", "department": "BIOCHEMISTRY", "result_name": "URINE METABOLIC SCREENING", "parameter_name": "URINE METABOLIC SCREENING", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.510409", "updated_at": "2025-07-08T19:27:21.510412"}, {"id": 602, "test_name": "Urine Organic Acid, Complete Panel", "test_code": "000195", "department": "BIOCHEMISTRY", "result_name": "Urine Organic Acid, Complete Panel", "parameter_name": "Urine Organic Acid, Complete Panel", "unit": null, "result_type": "Template", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "URINE", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.510519", "updated_at": "2025-07-08T19:27:21.510522"}, {"id": 603, "test_name": "URINE PHOSPHOROUS", "test_code": "000198", "department": "BIOCHEMISTRY", "result_name": "URINE PHOSPHOROUS", "parameter_name": "URINE PHOSPHOROUS", "unit": "mg/dL", "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Molybdate UV", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.510628", "updated_at": "2025-07-08T19:27:21.510631"}, {"id": 604, "test_name": "URINE URIC ACID", "test_code": "000201", "department": "BIOCHEMISTRY", "result_name": "URINE URIC ACID", "parameter_name": "URINE URIC ACID", "unit": "mg/dL", "result_type": null, "reference_range": "Random Urine Male < 40 yr : 9 - 63 >=40 yr : 6 - 114  Female < 40 yr : 6 - 71 >=40 yr : 4 - 93", "critical_low": null, "critical_high": null, "decimal_places": 1, "method": "Enzymatic", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.510736", "updated_at": "2025-07-08T19:27:21.510739"}, {"id": 605, "test_name": "URINE-CALCIUM", "test_code": "000252", "department": "BIOCHEMISTRY", "result_name": "URINE-CALCIUM", "parameter_name": "URINE-CALCIUM", "unit": "mg/dL", "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": "Colorimetric : 5-nitro-5’-methyl-BAPTA", "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.510843", "updated_at": "2025-07-08T19:27:21.510846"}, {"id": 606, "test_name": "VMA (SPOT)", "test_code": "000269", "department": "BIOCHEMISTRY", "result_name": "VMA (SPOT)", "parameter_name": "VMA (SPOT)", "unit": "mg/g creatinine", "result_type": null, "reference_range": "<18.80", "critical_low": null, "critical_high": null, "decimal_places": 2, "method": "HPLC", "specimen_type": "SPOT URINE", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.510952", "updated_at": "2025-07-08T19:27:21.510955"}, {"id": 607, "test_name": "VMA -24 H<PERSON> Urine", "test_code": "000204", "department": "BIOCHEMISTRY", "result_name": "VMA -24 H<PERSON> Urine", "parameter_name": "VMA -24 H<PERSON> Urine", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "24 Hr <PERSON><PERSON>", "container": "<PERSON><PERSON><PERSON>er", "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.511062", "updated_at": "2025-07-08T19:27:21.511065"}, {"id": 608, "test_name": "WATER ANALYSIS (CHEMISTRY)", "test_code": "000244", "department": "BIOCHEMISTRY", "result_name": "WATER ANALYSIS (CHEMISTRY)", "parameter_name": "WATER ANALYSIS (CHEMISTRY)", "unit": null, "result_type": "Pick List", "reference_range": null, "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": null, "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.511173", "updated_at": "2025-07-08T19:27:21.511176"}, {"id": 609, "test_name": "Whole Blood Clotting Test (WBCT)", "test_code": "001674", "department": "BIOCHEMISTRY", "result_name": "Whole Blood Clotting Test (WBCT)", "parameter_name": "Whole Blood Clotting Test (WBCT)", "unit": null, "result_type": "Pick List", "reference_range": "Less than 20 Mins.", "critical_low": null, "critical_high": null, "decimal_places": 0, "method": null, "specimen_type": "WHOLE BLOOD", "container": null, "instructions": null, "min_sample_qty": null, "excel_source": true, "source_sheet": "BioChemistry", "is_active": true, "created_at": "2025-07-08T19:27:21.511281", "updated_at": "2025-07-08T19:27:21.511283"}]