import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Form, Button, Row, Col, InputGroup, Alert, Badge } from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faArrowLeft, faSave, faPlus, faTrash, faSearch, faUser, faPhone,
  faExclamationTriangle, faCheckCircle, faTimes, faIdCard, faEnvelope,
  faCalendarAlt, faCreditCard, faFlask, faCalculator, faSpinner,
  faFileInvoiceDollar
} from '@fortawesome/free-solid-svg-icons';
import { patientAPI, billingAPI } from '../../services/api';
import billingService from '../../services/billingAPI';
import { useAuth } from '../../context/AuthContext';
import { useTenant } from '../../context/TenantContext';
import '../../styles/BillingRegistrationCompact.css';

// Compact Patient Search Component
const PatientSearch = ({ onPatientSelect, searchTerm, setSearchTerm, searchResults, searching }) => {
  const [showResults, setShowResults] = useState(false);

  return (
    <div className="patient-search-compact">
      <InputGroup size="sm">
        <InputGroup.Text>
          <FontAwesomeIcon icon={faSearch} />
        </InputGroup.Text>
        <Form.Control
          type="text"
          placeholder="Search existing patient (name/phone)..."
          value={searchTerm}
          onChange={(e) => {
            setSearchTerm(e.target.value);
            setShowResults(e.target.value.length > 0);
          }}
          onFocus={() => setShowResults(searchTerm.length > 0)}
        />
        {searchTerm && (
          <Button
            variant="outline-secondary"
            size="sm"
            onClick={() => {
              setSearchTerm('');
              setShowResults(false);
            }}
          >
            <FontAwesomeIcon icon={faTimes} />
          </Button>
        )}
      </InputGroup>
      
      {showResults && searchResults.length > 0 && (
        <div className="search-results-compact">
          {searchResults.slice(0, 5).map((patient) => (
            <div
              key={patient.id}
              className="search-result-item"
              onClick={() => {
                onPatientSelect(patient);
                setShowResults(false);
                setSearchTerm('');
              }}
            >
              <div className="patient-name">{patient.first_name} {patient.last_name}</div>
              <div className="patient-details">{patient.phone} • {patient.date_of_birth}</div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const BillingRegistration = () => {
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const { tenantData, currentTenantContext } = useTenant();

  // Core states
  const [loading, setLoading] = useState(false);
  const [sidGenerating, setSidGenerating] = useState(false);
  const [error, setError] = useState('');

  // Validation states
  const [fieldErrors, setFieldErrors] = useState({});
  const [fieldTouched, setFieldTouched] = useState({});
  const [validationSummary, setValidationSummary] = useState({
    isValid: false,
    completedFields: 0,
    totalRequiredFields: 0,
    missingFields: []
  });

  // UI states for progressive disclosure
  const [collapsedSections, setCollapsedSections] = useState({
    patientInfo: false,
    testSelection: false,
    billingDetails: false,
    paymentInfo: true // Start collapsed
  });
  const [success, setSuccess] = useState('');

  // Patient search states
  const [patientSearchTerm, setPatientSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [searching, setSearching] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState(null);

  // Enhanced form data state with all required fields
  const [formData, setFormData] = useState({
    // Section 1: Branch & Registration Details
    branch: currentUser?.tenant_id || 1,
    date: new Date().toISOString().split('T')[0],
    no: '', // Manual entry number
    sidDate: new Date().toISOString().split('T')[0],
    sidNo: '', // Auto-generated SID
    category: 'Normal',

    // Section 2: Patient Information
    patientCode: '',
    title: 'Mr.',
    patientName: '',
    firstName: '',
    lastName: '',
    dob: '',
    ageYears: '',
    ageMonths: '',
    ageInput: '', // Manual age entry
    ageMode: 'dob', // 'dob' or 'manual'
    sex: 'Male',
    mobile: '',
    email: '',
    referrer: 'Doctor',
    source: '',
    collectionBoy: '',
    motherName: '', // For baby patients

    // Section 3: Test Selection
    tests: [],

    // Section 4: Billing Details
    sampleCollectDateTime: new Date().toISOString().slice(0, 16),
    billAmount: 0,
    otherCharges: 0,
    otherChargesDescription: '',
    discountType: 'percentage', // 'percentage' or 'amount'
    discountPercent: 0,
    discountAmount: 0,
    discountRemarks: '',
    totalAmount: 0,
    amountPaid: 0,
    balanceToBePaid: 0,
    finalReportDate: '',

    // Section 5: Payment Details
    paymentMethod: 'Cash',
    bankName: '',
    referenceNumber: '',
    paymentDate: new Date().toISOString().split('T')[0],
    paymentAmount: 0,

    // Section 6: Clinical & Remarks
    clinicalRemarks: '',
    generalRemarks: '',
    emergency: false,
    deliveryMode: 'Lab Pickup',

    // Section 7: Study/Research Details
    studyNo: '',
    subPeriod: '',
    subjectPeriod: '',
    subNo: ''
  });

  // Test management
  const [availableTests, setAvailableTests] = useState([]);
  const [testSearchTerm, setTestSearchTerm] = useState('');

  // Enhanced master data options with proper tenant mapping
  const branchOptions = [
    { id: 1, name: 'MAYILADUTHURAI', site_code: 'MYD' },
    { id: 2, name: 'SIRKAZHI', site_code: 'SKZ' },
    { id: 3, name: 'THANJAVUR', site_code: 'TNJ' },
    { id: 4, name: 'KUMBAKONAM', site_code: 'KBK' }
  ];
  const categoryOptions = ['Normal', 'Emergency', 'VIP', 'Corporate'];
  const titleOptions = ['Mr.', 'Mrs.', 'Ms.', 'Dr.', 'Baby', 'Master', 'B/Q (Baby/Queen)'];
  const genderOptions = ['Male', 'Female', 'Others'];
  const referralSources = ['Doctor', 'Hospital', 'Corporate', 'Lab', 'Insurance', 'Self/Patient'];
  const referralOptions = {
    'Doctor': ['Dr. Rajesh Kumar', 'Dr. Priya Sharma', 'Dr. Arun Patel', 'Dr. Meera Singh', 'Dr. Suresh Reddy'],
    'Hospital': ['Apollo Hospital', 'Fortis Healthcare', 'Max Hospital', 'AIIMS', 'Government Hospital'],
    'Corporate': ['TCS Health', 'Infosys Wellness', 'Wipro Care', 'HCL Health', 'Tech Mahindra'],
    'Lab': ['SRL Diagnostics', 'Dr. Lal PathLabs', 'Metropolis', 'Thyrocare', 'Quest Diagnostics'],
    'Insurance': ['Star Health', 'HDFC ERGO', 'ICICI Lombard', 'New India Assurance', 'United India'],
    'Self/Patient': ['Walk-in Patient', 'Online Booking', 'Phone Booking', 'Repeat Customer']
  };
  const paymentMethods = ['Cash', 'Card', 'UPI', 'Bank Transfer', 'Cheque'];
  const deliveryModes = ['Home Delivery', 'Lab Pickup', 'Email', 'WhatsApp'];
  const collectionBoys = ['Ravi Kumar', 'Suresh M', 'Prakash S', 'Venkat R'];
  const bankOptions = ['SBI', 'HDFC', 'ICICI', 'Axis Bank', 'Canara Bank', 'Indian Bank'];
  const discountReasons = [
    'Senior Citizen Discount',
    'Employee Discount',
    'Corporate Package',
    'Bulk Test Discount',
    'Loyalty Customer',
    'Medical Emergency',
    'Insurance Adjustment',
    'Promotional Offer',
    'Repeat Customer',
    'Special Circumstances'
  ];

  // Validate all fields and update summary
  const validateAllFields = useCallback(() => {
    try {
      const errors = {};
      const requiredFields = Object.keys(validationRules || {});
      let completedFields = 0;
      const missingFields = [];

      requiredFields.forEach(fieldName => {
        let fieldValue;

        if (fieldName === 'tests') {
          fieldValue = formData.tests;
        } else {
          fieldValue = formData[fieldName];
        }

        const error = validateField(fieldName, fieldValue, formData);

        if (error) {
          errors[fieldName] = error;
          missingFields.push(validationRules[fieldName]?.label || fieldName);
        } else {
          completedFields++;
        }
      });

      setFieldErrors(errors);
      setValidationSummary({
        isValid: Object.keys(errors).length === 0,
        completedFields,
        totalRequiredFields: requiredFields.length,
        missingFields
      });

      return Object.keys(errors).length === 0;
    } catch (error) {
      console.error('Error in validateAllFields:', error);
      return false;
    }
  }, [formData]);

  // Load test profiles on mount
  useEffect(() => {
    loadTestProfiles();
    generateSID();
  }, []);

  // Run validation when form data changes
  useEffect(() => {
    validateAllFields();
  }, [validateAllFields]);

  const loadTestProfiles = async () => {
    try {
      // Try to fetch test profiles from master data API
      try {
        // First try to get test profiles from admin master data
        const response = await fetch('/api/admin/master-data', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const masterData = await response.json();
          if (masterData.success && masterData.data?.testProfiles) {
            const testProfiles = masterData.data.testProfiles.map(test => ({
              id: test.id,
              name: test.test_profile || test.testName || test.name,
              price: test.test_price || test.price || 0,
              department: test.department || 'General'
            }));
            setAvailableTests(testProfiles);
            return;
          }
        }
      } catch (apiError) {
        console.warn('Master data API not available, trying billing API:', apiError);
      }

      // Fallback to billing API for test data
      try {
        const response = await billingAPI.getAllBillings();
        if (response.success && response.data) {
          // If billing data contains test information, extract it
          const tests = response.data.flatMap(bill => bill.items || [])
            .filter((test, index, self) =>
              index === self.findIndex(t => t.test_id === test.test_id)
            )
            .map(test => ({
              id: test.test_id || test.id,
              name: test.test_name || test.testName || test.name,
              price: test.amount || test.price || 0
            }));

          if (tests.length > 0) {
            setAvailableTests(tests);
            return;
          }
        }
      } catch (apiError) {
        console.warn('Billing API not available, using mock data:', apiError);
      }

      // Mock test data as final fallback
      setAvailableTests([
        { id: 1, name: 'Complete Blood Count (CBC)', price: 300 },
        { id: 2, name: 'Lipid Profile', price: 450 },
        { id: 3, name: 'Liver Function Test', price: 500 },
        { id: 4, name: 'Kidney Function Test', price: 400 },
        { id: 5, name: 'Thyroid Profile', price: 600 },
        { id: 6, name: 'Blood Sugar (Fasting)', price: 150 },
        { id: 7, name: 'HbA1c', price: 350 },
        { id: 8, name: 'Vitamin D', price: 800 },
        { id: 9, name: 'Vitamin B12', price: 700 },
        { id: 10, name: 'ESR', price: 100 }
      ]);
    } catch (error) {
      console.error('Error loading test profiles:', error);
      setError('Failed to load test profiles. Please refresh the page.');
    }
  };

  // Generate SID number using backend API
  const generateSID = async (branchOverride = null) => {
    setSidGenerating(true);
    try {
      const targetBranch = branchOverride || formData.branch || currentUser?.tenant_id;
      console.log('Generating SID for branch:', targetBranch);

      const response = await fetch('/api/billing/generate-sid', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          branch: targetBranch
        })
      });

      if (response.ok) {
        const data = await response.json();
        console.log('SID generation response:', data);
        setFormData(prev => ({
          ...prev,
          sidNo: data.sid_number
        }));
      } else {
        console.error('SID generation API failed:', response.status, response.statusText);
        // Fallback to simple 3-digit format if API fails
        const fallbackSID = String(Math.floor(Math.random() * 900) + 100).padStart(3, '0');
        console.log('Using fallback SID:', fallbackSID);
        setFormData(prev => ({
          ...prev,
          sidNo: fallbackSID
        }));
      }
    } catch (error) {
      console.error('Error generating SID:', error);
      // Fallback to simple 3-digit format
      const fallbackSID = String(Math.floor(Math.random() * 900) + 100).padStart(3, '0');
      console.log('Using fallback SID due to error:', fallbackSID);
      setFormData(prev => ({
        ...prev,
        sidNo: fallbackSID
      }));
    } finally {
      setSidGenerating(false);
    }
  };

  // Helper function to safely format numbers
  const safeToFixed = (value, decimals = 2) => {
    const num = parseFloat(value);
    return isNaN(num) ? '0.00' : num.toFixed(decimals);
  };

  // Helper function to ensure numeric value
  const ensureNumber = (value) => {
    const num = parseFloat(value);
    return isNaN(num) ? 0 : num;
  };

  // Validation rules
  const validationRules = {
    patientName: { required: true, minLength: 2, label: 'Patient Name' },
  
    mobile: { required: true, pattern: /^[6-9]\d{9}$/, label: 'Mobile Number' },
    dob: { required: true, label: 'Date of Birth' },
    sex: { required: true, label: 'Gender' },
    branch: { required: true, label: 'Branch' },
    tests: { required: true, minLength: 1, label: 'Tests' },
    title: { required: true, label: 'Title' }
  };

  // Real-time field validation
  const validateField = (name, value, formData = {}) => {
    const rule = validationRules[name];
    if (!rule) return null;

    // Handle special cases
    if (name === 'tests') {
      if (!value || value.length === 0) {
        return 'At least one test must be selected';
      }
      return null;
    }

    if (name === 'mobile') {
      if (!value || value.trim() === '') {
        return 'Mobile number is required';
      }
      if (!/^[6-9]\d{9}$/.test(value.trim())) {
        return 'Mobile number must be 10 digits starting with 6-9';
      }
      return null;
    }

    if (name === 'dob') {
      if (!value) {
        return 'Date of birth is required';
      }
      const dobDate = new Date(value);
      const today = new Date();
      if (dobDate > today) {
        return 'Date of birth cannot be in the future';
      }
      return null;
    }

    // General validation
    if (rule.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
      return `${rule.label} is required`;
    }

    if (rule.minLength && value && value.length < rule.minLength) {
      return `${rule.label} must be at least ${rule.minLength} characters`;
    }

    if (rule.pattern && value && !rule.pattern.test(value)) {
      return `${rule.label} format is invalid`;
    }

    return null;
  };



  // Handle field blur for validation
  const handleFieldBlur = (fieldName) => {
    setFieldTouched(prev => ({ ...prev, [fieldName]: true }));

    let fieldValue;
    if (fieldName === 'tests') {
      fieldValue = formData.tests;
    } else {
      fieldValue = formData[fieldName];
    }

    const error = validateField(fieldName, fieldValue, formData);
    setFieldErrors(prev => ({
      ...prev,
      [fieldName]: error
    }));
  };

  // Age calculation functions
  const calculateAgeFromDOB = (dob) => {
    if (!dob) return { years: '', months: '' };

    const birthDate = new Date(dob);
    const today = new Date();

    let years = today.getFullYear() - birthDate.getFullYear();
    let months = today.getMonth() - birthDate.getMonth();

    if (months < 0) {
      years--;
      months += 12;
    }

    return { years: years.toString(), months: months.toString() };
  };

  // Auto-calculate age when DOB changes
  useEffect(() => {
    if (formData.dob && formData.ageMode === 'dob') {
      const { years, months } = calculateAgeFromDOB(formData.dob);
      setFormData(prev => ({
        ...prev,
        ageYears: years,
        ageMonths: months,
        ageInput: years > 0 ? `${years} years` : `${months} months`
      }));
    }
  }, [formData.dob, formData.ageMode]);

  // Check if patient is baby to show mother name field
  const isBabyPatient = () => {
    // Only show for specific baby titles, not based on age
    return formData.title === 'Baby' || formData.title === 'B/Q (Baby/Queen)';
  };

  // Helper function to get field validation state
  const getFieldValidationState = (fieldName) => {
    const hasError = fieldErrors[fieldName] && fieldTouched[fieldName];
    const isValid = !fieldErrors[fieldName] && fieldTouched[fieldName] && formData[fieldName];

    return {
      hasError,
      isValid,
      errorMessage: hasError ? fieldErrors[fieldName] : null,
      className: hasError ? 'is-invalid' : isValid ? 'is-valid' : ''
    };
  };

  // Section management
  const toggleSection = (sectionName) => {
    setCollapsedSections(prev => ({
      ...prev,
      [sectionName]: !prev[sectionName]
    }));
  };

  const getSectionCompletionStatus = (sectionName) => {
    switch (sectionName) {
      case 'patientInfo':
        return formData.patientName && formData.mobile && formData.dob && formData.sex;
      case 'testSelection':
        return formData.tests.length > 0;
      case 'billingDetails':
        return formData.totalAmount > 0;
      case 'paymentInfo':
        return formData.paymentMethod && formData.amountPaid >= 0;
      default:
        return false;
    }
  };

  // Patient search with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (patientSearchTerm.trim().length >= 2) {
        handlePatientSearch();
      } else {
        setSearchResults([]);
      }
    }, 300);
    return () => clearTimeout(timeoutId);
  }, [patientSearchTerm]);

  const handlePatientSearch = async () => {
    if (!patientSearchTerm.trim()) return;

    setSearching(true);
    try {
      const response = await patientAPI.searchPatients(patientSearchTerm);
      // Backend returns paginated data with structure {items: [...], page: 1, total_items: X}
      setSearchResults(response.data?.items || []);
    } catch (error) {
      console.error('Error searching patients:', error);
      setSearchResults([]);
    } finally {
      setSearching(false);
    }
  };

  // Handle patient selection from search
  const handlePatientSelect = async (patient) => {
    setSelectedPatient(patient);
    const { years, months } = calculateAgeFromDOB(patient.date_of_birth);

    setFormData(prev => ({
      ...prev,
      patientCode: patient.id || '',
      title: patient.title || 'Mr.',
      patientName: `${patient.first_name || ''} ${patient.last_name || ''}`.trim().toUpperCase(),
      firstName: patient.first_name || '',
      lastName: patient.last_name || '',
      dob: patient.date_of_birth || '',
      ageYears: years,
      ageMonths: months,
      ageInput: years > 0 ? `${years} years` : `${months} months`,
      ageMode: 'dob',
      sex: patient.gender || 'Male',
      mobile: patient.phone || '',
      email: patient.email || '',
      motherName: patient.mother_name || ''
    }));
    setPatientSearchTerm('');

    // Generate new SID for this billing session
    await generateSID();
  };

  // Handle form input changes with special logic
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === 'checkbox' ? checked : value;

    setFormData(prev => {
      const updated = { ...prev, [name]: newValue };

      // Auto-uppercase patient name
      if (name === 'patientName' || name === 'firstName' || name === 'lastName') {
        updated[name] = newValue.toUpperCase();
        if (name === 'firstName' || name === 'lastName') {
          updated.patientName = `${updated.firstName} ${updated.lastName}`.trim();
        }
      }

      // Auto-gender selection based on title
      if (name === 'title') {
        if (newValue === 'Mr.' || newValue === 'Master') {
          updated.sex = 'Male';
        } else if (newValue === 'Mrs.' || newValue === 'Ms.' || newValue === 'Miss') {
          updated.sex = 'Female';
        } else if (newValue === 'Baby' || newValue === 'B/Q (Baby/Queen)') {
          // For baby patients, keep current gender or default to Male
          if (!updated.sex) {
            updated.sex = 'Male';
          }
        }
        // For 'Dr.' and other titles, keep current gender selection
      }

      // Handle age mode switching
      if (name === 'ageMode') {
        if (newValue === 'manual') {
          updated.dob = '';
          updated.ageYears = '';
          updated.ageMonths = '';
          updated.ageInput = '';
        } else if (newValue === 'dob') {
          updated.ageYears = '';
          updated.ageMonths = '';
          updated.ageInput = '';
        }
      }

      // Auto-calculate age from DOB
      if (name === 'dob' && newValue) {
        const { years, months } = calculateAgeFromDOB(newValue);
        updated.ageYears = years;
        updated.ageMonths = months;
        updated.ageInput = years > 0 ? `${years} years` : `${months} months`;
      }

      // Auto-calculate DOB from manual age entry
      if ((name === 'ageYears' || name === 'ageMonths') && formData.ageMode === 'manual') {
        const years = name === 'ageYears' ? parseInt(newValue) || 0 : parseInt(updated.ageYears) || 0;
        const months = name === 'ageMonths' ? parseInt(newValue) || 0 : parseInt(updated.ageMonths) || 0;

        if (years > 0 || months > 0) {
          const today = new Date();
          const birthDate = new Date(today.getFullYear() - years, today.getMonth() - months, today.getDate());
          updated.dob = birthDate.toISOString().split('T')[0];
          updated.ageInput = years > 0 ? `${years} years` : `${months} months`;
        }
      }

      // Update age text when manually entered
      if (name === 'ageInput' && formData.ageMode === 'manual') {
        // Parse age input like "25 years" or "6 months"
        const ageText = newValue.toLowerCase();
        if (ageText.includes('year')) {
          const years = parseInt(ageText.match(/\d+/)?.[0]) || 0;
          updated.ageYears = years;
          updated.ageMonths = 0;
          if (years > 0) {
            const today = new Date();
            const birthDate = new Date(today.getFullYear() - years, today.getMonth(), today.getDate());
            updated.dob = birthDate.toISOString().split('T')[0];
          }
        } else if (ageText.includes('month')) {
          const months = parseInt(ageText.match(/\d+/)?.[0]) || 0;
          updated.ageYears = 0;
          updated.ageMonths = months;
          if (months > 0) {
            const today = new Date();
            const birthDate = new Date(today.getFullYear(), today.getMonth() - months, today.getDate());
            updated.dob = birthDate.toISOString().split('T')[0];
          }
        }
      }

      // Handle discount calculations
      if (name === 'discountPercent' && formData.discountType === 'percentage') {
        const billAmount = ensureNumber(formData.billAmount) + ensureNumber(formData.otherCharges);
        const discountAmount = (billAmount * ensureNumber(newValue)) / 100;
        updated.discountAmount = discountAmount;
        updated.totalAmount = billAmount - discountAmount;
        updated.balanceToBePaid = updated.totalAmount - ensureNumber(formData.amountPaid);
      }

      if (name === 'discountAmount' && formData.discountType === 'amount') {
        const billAmount = ensureNumber(formData.billAmount) + ensureNumber(formData.otherCharges);
        const discountAmount = ensureNumber(newValue);
        const discountPercent = billAmount > 0 ? (discountAmount / billAmount) * 100 : 0;
        updated.discountPercent = discountPercent;
        updated.totalAmount = billAmount - discountAmount;
        updated.balanceToBePaid = updated.totalAmount - ensureNumber(formData.amountPaid);
      }

      // Handle discount type switching
      if (name === 'discountType') {
        if (newValue === 'percentage') {
          // Keep percentage, recalculate amount
          const billAmount = ensureNumber(formData.billAmount) + ensureNumber(formData.otherCharges);
          const discountAmount = (billAmount * ensureNumber(formData.discountPercent)) / 100;
          updated.discountAmount = discountAmount;
        } else {
          // Keep amount, recalculate percentage
          const billAmount = ensureNumber(formData.billAmount) + ensureNumber(formData.otherCharges);
          const discountPercent = billAmount > 0 ? (ensureNumber(formData.discountAmount) / billAmount) * 100 : 0;
          updated.discountPercent = discountPercent;
        }
      }

      // Auto-calculate payment amount if not manually set
      if (name === 'amountPaid' && !updated.paymentAmount) {
        updated.paymentAmount = ensureNumber(newValue);
        updated.balanceToBePaid = ensureNumber(updated.totalAmount) - ensureNumber(newValue);
      }

      // Regenerate SID when branch changes
      if (name === 'branch' && newValue !== formData.branch) {
        // Regenerate SID for the new branch
        setTimeout(() => generateSID(newValue), 100);
      }

      return updated;
    });

    // Real-time validation for the changed field
    setTimeout(() => {
      const error = validateField(name, newValue, formData);
      setFieldErrors(prev => ({
        ...prev,
        [name]: error
      }));
      setFieldTouched(prev => ({ ...prev, [name]: true }));
    }, 100);
  };

  // Handle test addition
  const handleAddTest = (test) => {
    const newTest = {
      id: test.id,
      name: test.name,
      price: test.price
    };
    
    setFormData(prev => ({
      ...prev,
      tests: [...prev.tests, newTest]
    }));
    
    calculateTotals();
  };

  // Handle test removal
  const handleRemoveTest = (testId) => {
    setFormData(prev => ({
      ...prev,
      tests: prev.tests.filter(test => test.id !== testId)
    }));
    
    calculateTotals();
  };

  // Enhanced calculation function with safe number handling
  const calculateTotals = useCallback(() => {
    const testsTotal = formData.tests.reduce((sum, test) => sum + ensureNumber(test.price), 0);
    const billAmount = testsTotal + ensureNumber(formData.otherCharges);

    // Calculate discount
    let discountAmount = 0;
    if (formData.discountType === 'percentage') {
      discountAmount = (billAmount * ensureNumber(formData.discountPercent)) / 100;
    } else {
      discountAmount = ensureNumber(formData.discountAmount);
    }

    const totalAmount = billAmount - discountAmount;
    const balanceToBePaid = totalAmount - ensureNumber(formData.amountPaid);

    setFormData(prev => ({
      ...prev,
      billAmount,
      discountAmount: formData.discountType === 'percentage' ? discountAmount : prev.discountAmount,
      discountPercent: formData.discountType === 'amount' ? ensureNumber(safeToFixed((discountAmount / billAmount) * 100)) : prev.discountPercent,
      totalAmount,
      balanceToBePaid
    }));
  }, [formData.tests, formData.otherCharges, formData.discountType, formData.discountPercent, formData.discountAmount, formData.amountPaid]);

  // Recalculate when relevant fields change
  useEffect(() => {
    calculateTotals();
  }, [calculateTotals]);

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Enhanced validation
    if (formData.tests.length === 0) {
      setError('Please add at least one test');
      return;
    }

    if (!formData.patientName.trim() && (!formData.firstName.trim() || !formData.lastName.trim())) {
      setError('Patient name is required');
      return;
    }

    if (!formData.mobile.trim()) {
      setError('Mobile number is required');
      return;
    }

    if (!formData.dob) {
      setError('Date of birth is required');
      return;
    }

    if (!formData.branch) {
      setError('Please select a branch');
      return;
    }

    if (!formData.sidNo) {
      setError('SID number is required. Please wait for SID generation or refresh the page.');
      return;
    }

    // Validate discount remarks if discount is applied
    if ((ensureNumber(formData.discountPercent) > 0 || ensureNumber(formData.discountAmount) > 0) && !formData.discountRemarks.trim()) {
      setError('Discount remarks are required when applying discount');
      return;
    }

    // Validate mother's name for baby patients
    if (isBabyPatient() && !formData.motherName.trim()) {
      setError('Mother\'s name is required for baby patients');
      return;
    }

    setLoading(true);
    setError('');

    try {
      let patientId = selectedPatient?.id;

      // Create new patient if not selected from search
      if (!selectedPatient) {
        // Extract first and last name from patient name if not provided separately
        const nameParts = formData.patientName.trim().split(' ');
        const firstName = formData.firstName || nameParts[0] || '';
        const lastName = formData.lastName || nameParts.slice(1).join(' ') || '';

        // Validate required fields before creating patient
      
        if (!formData.mobile.trim()) {
          setError('Patient mobile number is required');
          return;
        }
        if (!formData.dob) {
          setError('Patient date of birth is required');
          return;
        }

        const patientData = {
          title: formData.title,
          first_name: firstName.trim(),
          last_name: lastName.trim(),
          gender: formData.sex,
          date_of_birth: formData.dob,
          phone: formData.mobile.trim(),
          email: formData.email?.trim() || '',
          address: formData.address?.trim() || '',
          mother_name: formData.motherName?.trim() || '',
          tenant_id: parseInt(formData.branch) || currentUser?.tenant_id
        };

        console.log('Creating patient with data:', patientData);

        try {
          const patientResponse = await patientAPI.createPatient(patientData);
          console.log('Patient creation response:', patientResponse);
          patientId = patientResponse.data.id;
        } catch (patientError) {
          console.error('Error creating patient:', patientError);
          const errorMessage = patientError.response?.data?.message || 'Failed to create patient record. Please check all required fields.';
          setError(`Patient Creation Error: ${errorMessage}`);
          return;
        }
      }

      // Prepare billing data matching backend expectations
      const billingData = {
        // Required fields for backend validation
        patient_id: patientId,
        items: formData.tests.map(test => ({
          test_id: test.id,
          test_name: test.name,
          amount: test.price,
          quantity: 1
        })),
        total_amount: formData.totalAmount || 0,

        // Additional billing information
        bill_amount: formData.billAmount || 0,
        other_charges: formData.otherCharges || 0,
        other_charges_description: formData.otherChargesDescription || '',
        discount_percent: formData.discountPercent || 0,
        discount: formData.discountAmount || 0,
        subtotal: formData.billAmount || 0,
        paid_amount: formData.amountPaid || 0,
        payment_method: formData.paymentMethod || '',
        notes: formData.generalRemarks || '',
        branch: parseInt(formData.branch) || currentUser?.tenant_id,

        // Extended billing information for comprehensive tracking
        patient_name: formData.patientName || `${formData.firstName} ${formData.lastName}`.trim().toUpperCase(),
        registration_date: formData.date,
        sid_number: formData.sidNo,
        category: formData.category,
        discount_type: formData.discountType,
        discount_remarks: formData.discountRemarks,
        balance_amount: formData.balanceToBePaid,
        payment_date: formData.paymentDate,
        payment_amount: formData.paymentAmount,
        bank_name: formData.bankName,
        reference_number: formData.referenceNumber,
        sample_collect_datetime: formData.sampleCollectDateTime,
        clinical_remarks: formData.clinicalRemarks,
        emergency: formData.emergency,
        delivery_mode: formData.deliveryMode,
        final_report_date: formData.finalReportDate,
        referrer: formData.referrer,
        referral_source: formData.source,
        collection_boy: formData.collectionBoy,
        mother_name: formData.motherName,
        study_number: formData.studyNo,
        sub_period: formData.subPeriod,
        subject_period: formData.subjectPeriod,
        subject_number: formData.subNo,
        created_by: currentUser?.id,
        tenant_id: parseInt(formData.branch) || currentUser?.tenant_id
      };

      const response = await billingService.createBilling(billingData);

      if (response.success) {
        setSuccess('Billing registration completed successfully!');
        setTimeout(() => {
          navigate('/billing/reports');
        }, 2000);
      } else {
        setError(response.error || 'Failed to create billing record');
      }
    } catch (error) {
      console.error('Error submitting billing:', error);
      setError('Failed to submit billing. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Clear form with all new fields
  const handleClear = () => {
    setFormData({
      // Section 1: Branch & Registration Details
      branch: currentUser?.tenant_id || 1,
      date: new Date().toISOString().split('T')[0],
      no: '',
      sidDate: new Date().toISOString().split('T')[0],
      sidNo: '',
      category: 'Normal',

      // Section 2: Patient Information
      patientCode: '',
      title: 'Mr.',
      patientName: '',
      firstName: '',
      lastName: '',
      dob: '',
      ageYears: '',
      ageMonths: '',
      ageInput: '',
      ageMode: 'dob',
      sex: 'Male',
      mobile: '',
      email: '',
      referrer: 'Doctor',
      source: '',
      collectionBoy: '',
      motherName: '',

      // Section 3: Test Selection
      tests: [],

      // Section 4: Billing Details
      sampleCollectDateTime: new Date().toISOString().slice(0, 16),
      billAmount: 0,
      otherCharges: 0,
      otherChargesDescription: '',
      discountType: 'percentage',
      discountPercent: 0,
      discountAmount: 0,
      discountRemarks: '',
      totalAmount: 0,
      amountPaid: 0,
      balanceToBePaid: 0,
      finalReportDate: '',

      // Section 5: Payment Details
      paymentMethod: 'Cash',
      bankName: '',
      referenceNumber: '',
      paymentDate: new Date().toISOString().split('T')[0],
      paymentAmount: 0,

      // Section 6: Clinical & Remarks
      clinicalRemarks: '',
      generalRemarks: '',
      emergency: false,
      deliveryMode: 'Lab Pickup',

      // Section 7: Study/Research Details
      studyNo: '',
      subPeriod: '',
      subjectPeriod: '',
      subNo: ''
    });
    setSelectedPatient(null);
    setPatientSearchTerm('');
    generateSID();
  };

  return (
    <div className="billing-registration-compact">
      {/* Header */}
      <div className="header-section">
        <div className="d-flex justify-content-between align-items-center">
          <div>
            <h4 className="mb-1">
              <FontAwesomeIcon icon={faFileInvoiceDollar} className="me-2" />
              Billing Registration
            </h4>
            <p className="text-muted mb-0 small">Single form for new & existing patients</p>
          </div>
          <div className="d-flex gap-2">
            <Button variant="outline-secondary" onClick={() => navigate('/billing/reports')}>
              <FontAwesomeIcon icon={faArrowLeft} className="me-1" />
              Back
            </Button>
            <Button variant="outline-warning" onClick={handleClear}>
              Clear Form
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Alerts */}
      {error && (
        <Alert variant="danger" className="mb-3 shadow-sm" style={{borderLeft: '4px solid #dc3545'}}>
          <div className="d-flex align-items-center">
            <FontAwesomeIcon icon={faExclamationTriangle} className="me-2 fs-5" />
            <div>
              <strong>Error:</strong> {error}
            </div>
          </div>
        </Alert>
      )}

      {success && (
        <Alert variant="success" className="mb-3 shadow-sm" style={{borderLeft: '4px solid #198754'}}>
          <div className="d-flex align-items-center">
            <FontAwesomeIcon icon={faCheckCircle} className="me-2 fs-5" />
            <div>
              <strong>Success:</strong> {success}
            </div>
          </div>
        </Alert>
      )}

      {/* Validation Summary */}
      {validationSummary.totalRequiredFields > 0 && (
        <Alert
          variant={validationSummary.isValid ? "success" : "warning"}
          className="mb-3 shadow-sm"
          style={{borderLeft: `4px solid ${validationSummary.isValid ? '#198754' : '#ffc107'}`}}
        >
          <div className="d-flex align-items-center justify-content-between">
            <div className="d-flex align-items-center">
              <FontAwesomeIcon
                icon={validationSummary.isValid ? faCheckCircle : faExclamationTriangle}
                className="me-2 fs-5"
              />
              <div>
                <strong>Form Validation:</strong> {validationSummary.completedFields}/{validationSummary.totalRequiredFields} required fields completed
                {!validationSummary.isValid && validationSummary.missingFields.length > 0 && (
                  <div className="small mt-1">
                    <strong>Missing:</strong> {validationSummary.missingFields.join(', ')}
                  </div>
                )}
              </div>
            </div>
            <div className="text-end">
              <div className="progress" style={{width: '100px', height: '8px'}}>
                <div
                  className="progress-bar"
                  style={{
                    width: `${(validationSummary.completedFields / validationSummary.totalRequiredFields) * 100}%`,
                    backgroundColor: validationSummary.isValid ? '#198754' : '#ffc107'
                  }}
                ></div>
              </div>
            </div>
          </div>
        </Alert>
      )}

      {/* Patient Search */}
      <div className="search-section mb-3">
        <PatientSearch
          onPatientSelect={handlePatientSelect}
          searchTerm={patientSearchTerm}
          setSearchTerm={setPatientSearchTerm}
          searchResults={searchResults}
          searching={searching}
        />
        {selectedPatient && (
          <div className="selected-patient-info">
            <Badge bg="success">
              <FontAwesomeIcon icon={faUser} className="me-1" />
              Existing Patient: {selectedPatient.first_name} {selectedPatient.last_name}
            </Badge>
          </div>
        )}
      </div>

      {/* Main Form */}
      <Form onSubmit={handleSubmit}>
        {/* Section 1: Branch & Registration Details */}
        <div className="form-section mb-3">
          <h6 className="section-title">
            <FontAwesomeIcon icon={faIdCard} className="me-2" />
            Branch & Registration Details
          </h6>
          <Row className="g-2">
            <Col md={2}>
              <Form.Group>
                <Form.Label className="small text-primary fw-bold">Branch *</Form.Label>
                <Form.Select
                  size="sm"
                  name="branch"
                  value={formData.branch}
                  onChange={handleInputChange}
                  required
                >
                  {branchOptions.map(branch => (
                    <option key={branch.id} value={branch.id}>{branch.name}</option>
                  ))}
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={2}>
              <Form.Group>
                <Form.Label className="small text-purple fw-bold">Date *</Form.Label>
                <Form.Control
                  size="sm"
                  type="date"
                  name="date"
                  value={formData.date}
                  onChange={handleInputChange}
                  required
                />
              </Form.Group>
            </Col>
            <Col md={2}>
              <Form.Group>
                <Form.Label className="small text-primary fw-bold">No.</Form.Label>
                <Form.Control
                  size="sm"
                  type="text"
                  name="no"
                  value={formData.no}
                  onChange={handleInputChange}
                  placeholder="Manual entry"
                />
              </Form.Group>
            </Col>
            <Col md={2}>
              <Form.Group>
                <Form.Label className="small text-purple fw-bold">SID Date</Form.Label>
                <Form.Control
                  size="sm"
                  type="date"
                  name="sidDate"
                  value={formData.sidDate}
                  onChange={handleInputChange}
                />
              </Form.Group>
            </Col>
            <Col md={2}>
              <Form.Group>
                <Form.Label className="small text-primary fw-bold">
                  SID No. *
                  {sidGenerating && (
                    <span className="ms-1 text-info">
                      <FontAwesomeIcon icon={faSpinner} spin className="me-1" />
                      Generating...
                    </span>
                  )}
                </Form.Label>
                <Form.Control
                  size="sm"
                  type="text"
                  name="sidNo"
                  value={formData.sidNo}
                  onChange={handleInputChange}
                  readOnly
                  placeholder={sidGenerating ? "Generating SID..." : "Auto-generated"}
                />
              </Form.Group>
            </Col>
            <Col md={2}>
              <Form.Group>
                <Form.Label className="small text-secondary fw-bold">Category</Form.Label>
                <Form.Select
                  size="sm"
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                >
                  {categoryOptions.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </Form.Select>
              </Form.Group>
            </Col>
          </Row>
        </div>

        <Row className="g-3">
          {/* Left Column - Patient Information */}
          <Col lg={4}>
            <div className="form-section">
              <h6 className="section-title">
                <FontAwesomeIcon icon={faUser} className="me-2" />
                Patient Information
              </h6>

              <Row className="g-2">
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small text-secondary fw-bold">Patient Code</Form.Label>
                    <Form.Control
                      size="sm"
                      type="text"
                      name="patientCode"
                      value={formData.patientCode}
                      onChange={handleInputChange}
                      placeholder="Auto-filled"
                    />
                  </Form.Group>
                </Col>
                <Col md={3}>
                  <Form.Group>
                    <Form.Label className="small text-secondary fw-bold">Title</Form.Label>
                    <Form.Select
                      size="sm"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                    >
                      {titleOptions.map(title => (
                        <option key={title} value={title}>{title}</option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={3}>
                  <Form.Group>
                    <Form.Label className="small text-primary fw-bold">
                      Sex *
                      {getFieldValidationState('sex').hasError && (
                        <FontAwesomeIcon icon={faExclamationTriangle} className="ms-1 text-danger" />
                      )}
                      {getFieldValidationState('sex').isValid && (
                        <FontAwesomeIcon icon={faCheckCircle} className="ms-1 text-success" />
                      )}
                    </Form.Label>
                    <Form.Select
                      size="sm"
                      name="sex"
                      value={formData.sex}
                      onChange={handleInputChange}
                      onBlur={() => handleFieldBlur('sex')}
                      className={getFieldValidationState('sex').className}
                      required
                    >
                      {genderOptions.map(gender => (
                        <option key={gender} value={gender}>{gender}</option>
                      ))}
                    </Form.Select>
                    {getFieldValidationState('sex').hasError && (
                      <div className="invalid-feedback d-block">
                        {getFieldValidationState('sex').errorMessage}
                      </div>
                    )}
                  </Form.Group>
                </Col>
              </Row>

              <Form.Group className="mt-2">
                <Form.Label className="small text-primary fw-bold">
                  Patient Name *
                  {getFieldValidationState('patientName').hasError && (
                    <FontAwesomeIcon icon={faExclamationTriangle} className="ms-1 text-danger" />
                  )}
                  {getFieldValidationState('patientName').isValid && (
                    <FontAwesomeIcon icon={faCheckCircle} className="ms-1 text-success" />
                  )}
                </Form.Label>
                <Form.Control
                  size="sm"
                  type="text"
                  name="patientName"
                  value={formData.patientName}
                  onChange={handleInputChange}
                  onBlur={() => handleFieldBlur('patientName')}
                  className={getFieldValidationState('patientName').className}
                  placeholder="Enter full name (auto-uppercase)"
                  required
                />
                {getFieldValidationState('patientName').hasError && (
                  <div className="invalid-feedback d-block">
                    {getFieldValidationState('patientName').errorMessage}
                  </div>
                )}
              </Form.Group>

              {/* Age Input Section */}
              <div className="mt-2">
                <Form.Label className="small text-secondary fw-bold">Age Entry Mode</Form.Label>
                <div className="d-flex gap-3 mb-2">
                  <Form.Check
                    type="radio"
                    name="ageMode"
                    value="dob"
                    checked={formData.ageMode === 'dob'}
                    onChange={handleInputChange}
                    label="Date of Birth"
                    className="small"
                  />
                  <Form.Check
                    type="radio"
                    name="ageMode"
                    value="manual"
                    checked={formData.ageMode === 'manual'}
                    onChange={handleInputChange}
                    label="Manual Age"
                    className="small"
                  />
                </div>

                {formData.ageMode === 'dob' ? (
                  <Row className="g-2">
                    <Col md={6}>
                      <Form.Group>
                        <Form.Label className="small text-purple fw-bold">Date of Birth</Form.Label>
                        <Form.Control
                          size="sm"
                          type="date"
                          name="dob"
                          value={formData.dob}
                          onChange={handleInputChange}
                        />
                      </Form.Group>
                    </Col>
                    <Col md={3}>
                      <Form.Group>
                        <Form.Label className="small text-secondary">Years</Form.Label>
                        <Form.Control
                          size="sm"
                          type="text"
                          value={formData.ageYears}
                          readOnly
                        />
                      </Form.Group>
                    </Col>
                    <Col md={3}>
                      <Form.Group>
                        <Form.Label className="small text-secondary">Months</Form.Label>
                        <Form.Control
                          size="sm"
                          type="text"
                          value={formData.ageMonths}
                          readOnly
                        />
                      </Form.Group>
                    </Col>
                  </Row>
                ) : (
                  <Row className="g-2">
                    <Col md={4}>
                      <Form.Group>
                        <Form.Label className="small text-secondary fw-bold">Years</Form.Label>
                        <Form.Control
                          size="sm"
                          type="number"
                          name="ageYears"
                          value={formData.ageYears}
                          onChange={handleInputChange}
                          placeholder="25"
                        />
                      </Form.Group>
                    </Col>
                    <Col md={4}>
                      <Form.Group>
                        <Form.Label className="small text-secondary fw-bold">Months</Form.Label>
                        <Form.Control
                          size="sm"
                          type="number"
                          name="ageMonths"
                          value={formData.ageMonths}
                          onChange={handleInputChange}
                          placeholder="6"
                        />
                      </Form.Group>
                    </Col>
                    <Col md={4}>
                      <Form.Group>
                        <Form.Label className="small text-secondary">Age Text</Form.Label>
                        <Form.Control
                          size="sm"
                          type="text"
                          name="ageInput"
                          value={formData.ageInput}
                          onChange={handleInputChange}
                          placeholder="25 years"
                        />
                      </Form.Group>
                    </Col>
                  </Row>
                )}
              </div>

              <Row className="g-2 mt-2">
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small text-primary fw-bold">
                      Mobile *
                      {getFieldValidationState('mobile').hasError && (
                        <FontAwesomeIcon icon={faExclamationTriangle} className="ms-1 text-danger" />
                      )}
                      {getFieldValidationState('mobile').isValid && (
                        <FontAwesomeIcon icon={faCheckCircle} className="ms-1 text-success" />
                      )}
                    </Form.Label>
                    <Form.Control
                      size="sm"
                      type="tel"
                      name="mobile"
                      value={formData.mobile}
                      onChange={handleInputChange}
                      onBlur={() => handleFieldBlur('mobile')}
                      className={getFieldValidationState('mobile').className}
                      placeholder="10-digit mobile (6-9 start)"
                      required
                    />
                    {getFieldValidationState('mobile').hasError && (
                      <div className="invalid-feedback d-block">
                        {getFieldValidationState('mobile').errorMessage}
                      </div>
                    )}
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small text-secondary fw-bold">Email</Form.Label>
                    <Form.Control
                      size="sm"
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="<EMAIL>"
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Row className="g-2 mt-2">
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small text-secondary fw-bold">Referrer Type</Form.Label>
                    <Form.Select
                      size="sm"
                      name="referrer"
                      value={formData.referrer}
                      onChange={handleInputChange}
                    >
                      {referralSources.map(source => (
                        <option key={source} value={source}>{source}</option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small text-secondary fw-bold">Referral Source</Form.Label>
                    <Form.Select
                      size="sm"
                      name="source"
                      value={formData.source}
                      onChange={handleInputChange}
                    >
                      <option value="">Select {formData.referrer}</option>
                      {referralOptions[formData.referrer]?.map(option => (
                        <option key={option} value={option}>{option}</option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
              </Row>

              <Form.Group className="mt-2">
                <Form.Label className="small text-secondary fw-bold">Collection Boy</Form.Label>
                <Form.Select
                  size="sm"
                  name="collectionBoy"
                  value={formData.collectionBoy}
                  onChange={handleInputChange}
                >
                  <option value="">Select Collection Boy</option>
                  {collectionBoys.map(boy => (
                    <option key={boy} value={boy}>{boy}</option>
                  ))}
                </Form.Select>
              </Form.Group>

              {/* Mother Name for Baby Patients */}
              {isBabyPatient() && (
                <Form.Group className="mt-2">
                  <Form.Label className="small text-primary fw-bold">Mother's Name *</Form.Label>
                  <Form.Control
                    size="sm"
                    type="text"
                    name="motherName"
                    value={formData.motherName}
                    onChange={handleInputChange}
                    placeholder="Mother's full name"
                    required={isBabyPatient()}
                  />
                </Form.Group>
              )}
            </div>
          </Col>

          {/* Center Column - Test Selection */}
          <Col lg={4}>
            <div className="form-section">
              <h6 className="section-title">
                <FontAwesomeIcon icon={faFlask} className="me-2" />
                Test/Profile Selection
              </h6>

              {/* Test Selection */}
              <div className="test-selection mb-3">
                <Form.Label className="small text-primary fw-bold">Select Test/Profile *</Form.Label>
                <InputGroup size="sm">
                  <Form.Control
                    type="text"
                    placeholder="Search tests/profiles..."
                    value={testSearchTerm}
                    onChange={(e) => setTestSearchTerm(e.target.value)}
                  />
                  <InputGroup.Text>
                    <FontAwesomeIcon icon={faFlask} />
                  </InputGroup.Text>
                </InputGroup>

                {testSearchTerm && (
                  <div className="test-dropdown">
                    {availableTests
                      .filter(test => test.name.toLowerCase().includes(testSearchTerm.toLowerCase()))
                      .slice(0, 8)
                      .map(test => (
                        <div
                          key={test.id}
                          className="test-item"
                          onClick={() => {
                            handleAddTest(test);
                            setTestSearchTerm('');
                          }}
                        >
                          <div className="test-details">
                            <div className="test-code">T{test.id.toString().padStart(3, '0')}</div>
                            <div className="test-name">{test.name}</div>
                          </div>
                          <span className="test-price">₹{test.price}</span>
                        </div>
                      ))
                    }
                  </div>
                )}
              </div>

              {/* Selected Tests Table */}
              {formData.tests.length > 0 && (
                <div className="selected-tests mb-3">
                  <Form.Label className="small text-success fw-bold">Selected Tests ({formData.tests.length})</Form.Label>
                  <div className="tests-table">
                    <div className="table-header">
                      <span>Code</span>
                      <span>Test Name</span>
                      <span>Price</span>
                      <span>Action</span>
                    </div>
                    {formData.tests.map(test => (
                      <div key={test.id} className="test-row">
                        <span className="test-code">T{test.id.toString().padStart(3, '0')}</span>
                        <span className="test-name">{test.name}</span>
                        <span className="test-price">₹{test.price}</span>
                        <Button
                          variant="outline-danger"
                          size="sm"
                          onClick={() => handleRemoveTest(test.id)}
                          className="remove-btn"
                        >
                          <FontAwesomeIcon icon={faTimes} />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {formData.tests.length === 0 && (
                <div className="no-tests-message">
                  <p className="text-muted small text-center py-3">
                    <FontAwesomeIcon icon={faFlask} className="me-2" />
                    No tests selected. Search and add tests above.
                  </p>
                </div>
              )}
            </div>
          </Col>

          {/* Right Column - Billing Details */}
          <Col lg={4}>
            <div className="form-section">
              <h6 className="section-title">
                <FontAwesomeIcon icon={faCreditCard} className="me-2" />
                Billing Details
              </h6>

              <Form.Group className="mb-2">
                <Form.Label className="small text-purple fw-bold">Sample Collect Date Time</Form.Label>
                <Form.Control
                  size="sm"
                  type="datetime-local"
                  name="sampleCollectDateTime"
                  value={formData.sampleCollectDateTime}
                  onChange={handleInputChange}
                />
              </Form.Group>

              {/* Billing Calculations */}
              <div className="billing-calculations">
                <Row className="g-2">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="small text-success fw-bold">Bill Amount</Form.Label>
                      <Form.Control
                        size="sm"
                        type="number"
                        value={safeToFixed(formData.billAmount)}
                        readOnly
                        className="fw-bold"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="small text-success fw-bold">Other Charges</Form.Label>
                      <Form.Control
                        size="sm"
                        type="number"
                        name="otherCharges"
                        value={formData.otherCharges}
                        onChange={handleInputChange}
                        placeholder="0.00"
                      />
                    </Form.Group>
                  </Col>
                </Row>

                {/* Other Charges Description */}
                {formData.otherCharges > 0 && (
                  <Form.Group className="mt-2">
                    <Form.Label className="small text-success fw-bold">Other Charges Description</Form.Label>
                    <Form.Control
                      size="sm"
                      type="text"
                      name="otherChargesDescription"
                      value={formData.otherChargesDescription}
                      onChange={handleInputChange}
                      placeholder="Describe the additional charges (e.g., Home collection, Express service, etc.)"
                    />
                  </Form.Group>
                )}

                <Row className="g-2 mt-2">
                </Row>

                {/* Discount Section */}
                <div className="discount-section mt-2">
                  <Form.Label className="small text-success fw-bold">Discount Options</Form.Label>
                  <div className="d-flex gap-3 mb-2">
                    <Form.Check
                      type="radio"
                      name="discountType"
                      value="percentage"
                      checked={formData.discountType === 'percentage'}
                      onChange={handleInputChange}
                      label="Percentage"
                      className="small"
                    />
                    <Form.Check
                      type="radio"
                      name="discountType"
                      value="amount"
                      checked={formData.discountType === 'amount'}
                      onChange={handleInputChange}
                      label="Fixed Amount"
                      className="small"
                    />
                  </div>

                  <Row className="g-2">
                    <Col md={6}>
                      <Form.Group>
                        <Form.Label className="small text-success">
                          {formData.discountType === 'percentage' ? 'Discount %' : 'Discount Amount'}
                        </Form.Label>
                        <Form.Control
                          size="sm"
                          type="number"
                          name={formData.discountType === 'percentage' ? 'discountPercent' : 'discountAmount'}
                          value={formData.discountType === 'percentage' ? formData.discountPercent : formData.discountAmount}
                          onChange={handleInputChange}
                          placeholder="0"
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group>
                        <Form.Label className="small text-success">Discount Value</Form.Label>
                        <Form.Control
                          size="sm"
                          type="number"
                          value={safeToFixed(formData.discountAmount)}
                          readOnly
                          className="text-success fw-bold"
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  {(formData.discountPercent > 0 || formData.discountAmount > 0) && (
                    <Form.Group className="mt-2">
                      <Form.Label className="small text-success fw-bold">Discount Remarks *</Form.Label>
                      <Form.Select
                        size="sm"
                        name="discountRemarks"
                        value={formData.discountRemarks}
                        onChange={handleInputChange}
                        required={formData.discountPercent > 0 || formData.discountAmount > 0}
                      >
                        <option value="">Select discount reason</option>
                        {discountReasons.map(reason => (
                          <option key={reason} value={reason}>{reason}</option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  )}
                </div>

                <Row className="g-2 mt-2">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="small text-success fw-bold">Total Amount</Form.Label>
                      <Form.Control
                        size="sm"
                        type="number"
                        value={safeToFixed(formData.totalAmount)}
                        readOnly
                        className="fw-bold text-primary"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="small text-success fw-bold">Amount Paid</Form.Label>
                      <Form.Control
                        size="sm"
                        type="number"
                        name="amountPaid"
                        value={formData.amountPaid}
                        onChange={handleInputChange}
                        placeholder="0.00"
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Row className="g-2 mt-1">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="small text-success fw-bold">Balance to be Paid</Form.Label>
                      <Form.Control
                        size="sm"
                        type="number"
                        value={safeToFixed(formData.balanceToBePaid)}
                        readOnly
                        className={formData.balanceToBePaid > 0 ? 'text-danger fw-bold' : 'text-success fw-bold'}
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="small text-purple fw-bold">Final Report Date</Form.Label>
                      <Form.Control
                        size="sm"
                        type="date"
                        name="finalReportDate"
                        value={formData.finalReportDate}
                        onChange={handleInputChange}
                      />
                    </Form.Group>
                  </Col>
                </Row>
              </div>
            </div>
          </Col>
        </Row>

        {/* Bottom Sections */}
        <Row className="g-3 mt-3">
          {/* Section 5: Payment Details */}
          <Col lg={4}>
            <div className="form-section">
              <h6 className="section-title">
                <FontAwesomeIcon icon={faCreditCard} className="me-2" />
                Payment Details
              </h6>

              <Row className="g-2">
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small text-success fw-bold">Payment Method</Form.Label>
                    <Form.Select
                      size="sm"
                      name="paymentMethod"
                      value={formData.paymentMethod}
                      onChange={handleInputChange}
                    >
                      {paymentMethods.map(method => (
                        <option key={method} value={method}>{method}</option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small text-purple fw-bold">Payment Date</Form.Label>
                    <Form.Control
                      size="sm"
                      type="date"
                      name="paymentDate"
                      value={formData.paymentDate}
                      onChange={handleInputChange}
                    />
                  </Form.Group>
                </Col>
              </Row>

              {/* Conditional Bank Details */}
              {(formData.paymentMethod === 'Bank Transfer' || formData.paymentMethod === 'Cheque') && (
                <Row className="g-2 mt-2">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="small text-success fw-bold">Bank Name</Form.Label>
                      <Form.Select
                        size="sm"
                        name="bankName"
                        value={formData.bankName}
                        onChange={handleInputChange}
                      >
                        <option value="">Select Bank</option>
                        {bankOptions.map(bank => (
                          <option key={bank} value={bank}>{bank}</option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="small text-success fw-bold">Reference Number</Form.Label>
                      <Form.Control
                        size="sm"
                        type="text"
                        name="referenceNumber"
                        value={formData.referenceNumber}
                        onChange={handleInputChange}
                        placeholder="Transaction/Cheque No."
                      />
                    </Form.Group>
                  </Col>
                </Row>
              )}

              <Form.Group className="mt-2">
                <Form.Label className="small text-success fw-bold">Payment Amount</Form.Label>
                <Form.Control
                  size="sm"
                  type="number"
                  name="paymentAmount"
                  value={formData.paymentAmount}
                  onChange={handleInputChange}
                  placeholder="Actual payment amount"
                />
              </Form.Group>
            </div>
          </Col>

          {/* Section 6: Clinical & Remarks */}
          <Col lg={4}>
            <div className="form-section">
              <h6 className="section-title">
                <FontAwesomeIcon icon={faUser} className="me-2" />
                Clinical & Remarks
              </h6>

              <Form.Group className="mb-2">
                <Form.Label className="small text-orange fw-bold">Clinical Remarks</Form.Label>
                <Form.Control
                  size="sm"
                  as="textarea"
                  rows={2}
                  name="clinicalRemarks"
                  value={formData.clinicalRemarks}
                  onChange={handleInputChange}
                  placeholder="Medical notes and observations"
                />
              </Form.Group>

              <Form.Group className="mb-2">
                <Form.Label className="small text-secondary fw-bold">General Remarks</Form.Label>
                <Form.Control
                  size="sm"
                  as="textarea"
                  rows={2}
                  name="generalRemarks"
                  value={formData.generalRemarks}
                  onChange={handleInputChange}
                  placeholder="Additional notes"
                />
              </Form.Group>

              <Row className="g-2">
                <Col md={6}>
                  <Form.Group>
                    <Form.Check
                      type="checkbox"
                      name="emergency"
                      checked={formData.emergency}
                      onChange={handleInputChange}
                      label="Emergency Case"
                      className="small text-orange fw-bold"
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small text-secondary fw-bold">Delivery Mode</Form.Label>
                    <Form.Select
                      size="sm"
                      name="deliveryMode"
                      value={formData.deliveryMode}
                      onChange={handleInputChange}
                    >
                      {deliveryModes.map(mode => (
                        <option key={mode} value={mode}>{mode}</option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
              </Row>
            </div>
          </Col>

          {/* Section 7: Study/Research Details */}
          <Col lg={4}>
            <div className="form-section">
              <h6 className="section-title">
                <FontAwesomeIcon icon={faFlask} className="me-2" />
                Study/Research Details
              </h6>

              <Row className="g-2">
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small text-secondary fw-bold">Study No</Form.Label>
                    <Form.Control
                      size="sm"
                      type="text"
                      name="studyNo"
                      value={formData.studyNo}
                      onChange={handleInputChange}
                      placeholder="Research study ID"
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small text-secondary fw-bold">Sub. Period</Form.Label>
                    <Form.Control
                      size="sm"
                      type="text"
                      name="subPeriod"
                      value={formData.subPeriod}
                      onChange={handleInputChange}
                      placeholder="Sub-period"
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Row className="g-2 mt-2">
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small text-secondary fw-bold">Subject Period</Form.Label>
                    <Form.Control
                      size="sm"
                      type="text"
                      name="subjectPeriod"
                      value={formData.subjectPeriod}
                      onChange={handleInputChange}
                      placeholder="Subject observation period"
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small text-secondary fw-bold">Sub No.</Form.Label>
                    <Form.Control
                      size="sm"
                      type="text"
                      name="subNo"
                      value={formData.subNo}
                      onChange={handleInputChange}
                      placeholder="Subject number"
                    />
                  </Form.Group>
                </Col>
              </Row>
            </div>
          </Col>
        </Row>

        {/* Enhanced Submit Section */}
        <div className="form-section mt-4">
          <div className="d-flex justify-content-between align-items-center">
            <div className="text-muted small">
              {formData.tests.length === 0 ? (
                <span className="text-warning">
                  <FontAwesomeIcon icon={faExclamationTriangle} className="me-1" />
                  Please add at least one test to proceed
                </span>
              ) : (
                <span className="text-success">
                  <FontAwesomeIcon icon={faCheckCircle} className="me-1" />
                  {formData.tests.length} test(s) selected • Total: ₹{safeToFixed(formData.totalAmount)}
                </span>
              )}
            </div>
            <div className="d-flex gap-2">
              <Button
                variant="outline-secondary"
                onClick={handleClear}
                disabled={loading}
                className="px-3"
              >
                <FontAwesomeIcon icon={faTrash} className="me-1" />
                Clear
              </Button>
              <Button
                variant="success"
                type="submit"
                disabled={loading || formData.tests.length === 0}
                className="px-4 shadow"
                style={{
                  background: loading ? '#6c757d' : 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',
                  border: 'none',
                  minWidth: '200px'
                }}
              >
                <FontAwesomeIcon
                  icon={loading ? faSpinner : faSave}
                  spin={loading}
                  className="me-2"
                />
                {loading ? 'Processing...' : 'Save Billing Registration'}
              </Button>
            </div>
          </div>
        </div>
      </Form>
    </div>
  );
};

export default BillingRegistration;
