import React, { useState, useEffect } from 'react';
import { Card, Button, Form, Row, Col, Alert, Badge, Table, Modal } from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faPlus, faEdit, faTrash, faSearch, faSync,
  faFlask, faVial, faDatabase, faDownload
} from '@fortawesome/free-solid-svg-icons';
import { adminAPI } from '../../services/api';
import {
  TextInput,
  NumberInput,
  DeleteConfirmationModal,
  SuccessModal,
  ErrorModal,
  FormModal
} from '../common';

const UnifiedTestResultMaster = () => {
  // State management
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');

  // Data states
  const [testMasterData, setTestMasterData] = useState([]);
  const [resultMasterData, setResultMasterData] = useState([]);
  const [excelData, setExcelData] = useState([]);

  // Form states
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [formData, setFormData] = useState({});

  // Auto-population states
  const [autoPopulating, setAutoPopulating] = useState(false);
  const [autoPopulationStatus, setAutoPopulationStatus] = useState({});

  // Modal states
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [itemToDelete, setItemToDelete] = useState(null);

  // Initial form data structure
  const getInitialFormData = () => ({
    // Basic Information
    department: '',
    testName: '',
    test_code: '',
    short_name: '',

    // Test Master Fields
    hmsCode: '',
    method: '',
    method_code: '',
    specimen: [],
    specimen_code: '',
    container: '',
    container_code: '',
    instructions: '',
    min_sample_qty: '',
    test_price: 0,
    serviceTime: '24 Hours',
    reporting_days: 0,
    test_done_on: 'all',
    applicable_to: 'Both',

    // Result Master Fields
    result_name: '',
    parameter_name: '',
    unit: '',
    result_type: 'Numeric',
    reference_range: '',
    critical_low: '',
    critical_high: '',
    decimal_places: 0,

    // Common Fields
    excel_source: false,
    is_active: true
  });

  // Fetch data on component mount
  useEffect(() => {
    fetchAllData();
  }, []);

  const fetchAllData = async () => {
    setLoading(true);
    try {
      const [testMasterResponse, resultMasterResponse, excelResponse] = await Promise.all([
        adminAPI.get('/admin/test-master-enhanced'),
        adminAPI.get('/admin/result-master-enhanced'),
        adminAPI.get('/admin/excel-data')
      ]);

      setTestMasterData(testMasterResponse.data.data || []);
      setResultMasterData(resultMasterResponse.data.data || []);
      setExcelData(excelResponse.data.data || []);
    } catch (err) {
      console.error('Error fetching data:', err);
      setError('Failed to load data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Auto-population functionality
  const handleTestCodeChange = async (e) => {
    const testCode = e.target.value;
    setFormData(prev => ({ ...prev, test_code: testCode }));

    if (testCode && testCode.length >= 3) {
      await performAutoPopulation('code', testCode);
    }
  };

  const handleTestNameChange = async (e) => {
    const testName = e.target.value;
    setFormData(prev => ({ ...prev, testName: testName }));

    if (testName && testName.length >= 3) {
      await performAutoPopulation('name', testName);
    }
  };

  const performAutoPopulation = async (searchType, searchValue) => {
    setAutoPopulating(true);
    setAutoPopulationStatus({});

    try {
      let response;
      if (searchType === 'code') {
        response = await adminAPI.get(`/admin/excel-data/lookup/${searchValue}`);
      } else {
        response = await adminAPI.get(`/admin/excel-data/lookup-by-name/${encodeURIComponent(searchValue)}`);
      }

      if (response.data.found && response.data.data) {
        const excelItem = response.data.data;

        // Auto-populate form fields
        const autoPopulatedData = {
          ...formData,
          // Basic Information
          department: excelItem.department || formData.department,
          testName: excelItem.test_name || formData.testName,
          test_code: excelItem.test_code || formData.test_code,
          short_name: excelItem.short_name || formData.short_name,

          // Test Master Fields
          hmsCode: excelItem.test_code || formData.hmsCode,
          method: excelItem.method || formData.method,
          method_code: excelItem.method_code || formData.method_code,
          specimen: excelItem.specimen ? [excelItem.specimen] : formData.specimen,
          specimen_code: excelItem.specimen_code || formData.specimen_code,
          container: excelItem.container || formData.container,
          container_code: excelItem.container_code || formData.container_code,
          instructions: excelItem.instructions || formData.instructions,
          min_sample_qty: excelItem.min_sample_qty || formData.min_sample_qty,
          test_price: excelItem.price || formData.test_price,
          reporting_days: excelItem.reporting_days || formData.reporting_days,
          test_done_on: excelItem.test_done_on || formData.test_done_on,
          applicable_to: excelItem.applicable_to || formData.applicable_to,

          // Result Master Fields
          result_name: excelItem.test_name || formData.result_name,
          parameter_name: excelItem.test_name || formData.parameter_name,
          unit: excelItem.result_unit || formData.unit,
          result_type: excelItem.result_type || formData.result_type,
          reference_range: excelItem.reference_range || formData.reference_range,
          critical_low: excelItem.critical_low || formData.critical_low,
          critical_high: excelItem.critical_high || formData.critical_high,
          decimal_places: excelItem.decimals || formData.decimal_places,

          // Mark as auto-populated
          excel_source: true
        };

        setFormData(autoPopulatedData);

        // Set auto-population status
        const populatedFields = {};
        Object.keys(autoPopulatedData).forEach(key => {
          if (excelItem[key] || (key === 'testName' && excelItem.test_name)) {
            populatedFields[key] = { populated: true, source: 'Excel' };
          }
        });

        setAutoPopulationStatus(populatedFields);

        console.log('✅ Auto-population successful:', {
          searchType,
          searchValue,
          fieldsPopulated: Object.keys(populatedFields).length
        });
      } else {
        setAutoPopulationStatus({ notFound: true });
        console.log('⚠️ No Excel data found for:', searchType, searchValue);
      }
    } catch (err) {
      console.error('Auto-population error:', err);
      setAutoPopulationStatus({ error: true });
    } finally {
      setAutoPopulating(false);
    }
  };

  // Form handling
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleAddClick = () => {
    setFormData(getInitialFormData());
    setAutoPopulationStatus({});
    setShowAddModal(true);
  };

  const handleEditClick = (item) => {
    setEditingItem(item);
    setFormData(item);
    setAutoPopulationStatus({});
    setShowEditModal(true);
  };

  const handleAddSubmit = async (e) => {
    e.preventDefault();
    try {
      // Add to both test master and result master
      await Promise.all([
        adminAPI.post('/admin/test-master-enhanced', formData),
        adminAPI.post('/admin/result-master-enhanced', formData)
      ]);

      setShowAddModal(false);
      setShowSuccessModal(true);
      fetchAllData();
    } catch (err) {
      console.error('Error adding item:', err);
      setErrorMessage('Failed to add item. Please try again.');
      setShowErrorModal(true);
    }
  };

  const handleEditSubmit = async (e) => {
    e.preventDefault();
    try {
      // Update both test master and result master
      await Promise.all([
        adminAPI.put(`/admin/test-master-enhanced/${editingItem.id}`, formData),
        adminAPI.put(`/admin/result-master-enhanced/${editingItem.id}`, formData)
      ]);

      setShowEditModal(false);
      setShowSuccessModal(true);
      fetchAllData();
    } catch (err) {
      console.error('Error updating item:', err);
      setErrorMessage('Failed to update item. Please try again.');
      setShowErrorModal(true);
    }
  };

  const handleDeleteClick = (item) => {
    setItemToDelete(item);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      // Delete from both test master and result master
      await Promise.all([
        adminAPI.delete(`/admin/test-master-enhanced/${itemToDelete.id}`),
        adminAPI.delete(`/admin/result-master-enhanced/${itemToDelete.id}`)
      ]);

      setShowDeleteModal(false);
      setShowSuccessModal(true);
      fetchAllData();
    } catch (err) {
      console.error('Error deleting item:', err);
      setErrorMessage('Failed to delete item. Please try again.');
      setShowErrorModal(true);
    }
  };

  // Filter data based on search
  const getFilteredData = () => {
    if (!searchQuery) return testMasterData;

    const query = searchQuery.toLowerCase();
    return testMasterData.filter(item =>
      item.testName?.toLowerCase().includes(query) ||
      item.test_code?.toLowerCase().includes(query) ||
      item.department?.toLowerCase().includes(query)
    );
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-2">Loading unified test and result master data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="unified-test-result-master">
      <Card>
        <Card.Header className="d-flex justify-content-between align-items-center">
          <div>
            <h5 className="mb-0">
              <FontAwesomeIcon icon={faDatabase} className="me-2" />
              Unified Test & Result Master
            </h5>
            <small className="text-muted">
              Combined Test Master and Result Master with Excel auto-population
            </small>
          </div>
          <div className="d-flex gap-2">
            <Button variant="success" onClick={handleAddClick}>
              <FontAwesomeIcon icon={faPlus} className="me-1" />
              Add New
            </Button>
            <Button variant="outline-primary" onClick={fetchAllData}>
              <FontAwesomeIcon icon={faSync} className="me-1" />
              Refresh
            </Button>
          </div>
        </Card.Header>

        <div className="card-header-search py-2 px-3 border-bottom">
          <Row>
            <Col md={6}>
              <Form.Control
                type="text"
                placeholder="Search by test name, code, or department..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </Col>
            <Col md={6} className="text-end">
              <Badge bg="info" className="me-2">
                Test Master: {testMasterData.length}
              </Badge>
              <Badge bg="success" className="me-2">
                Result Master: {resultMasterData.length}
              </Badge>
              <Badge bg="warning">
                Excel Data: {excelData.length}
              </Badge>
            </Col>
          </Row>
        </div>

        <Card.Body>
          {error && <Alert variant="danger">{error}</Alert>}

          <div className="table-responsive">
            <Table className="table-hover">
              <thead>
                <tr>
                  <th>Test Code</th>
                  <th>Test Name</th>
                  <th>Department</th>
                  <th>Price</th>
                  <th>Unit</th>
                  <th>Reference Range</th>
                  <th>Source</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {getFilteredData().map(item => (
                  <tr key={item.id}>
                    <td>
                      <code>{item.test_code || item.hmsCode}</code>
                    </td>
                    <td>{item.testName}</td>
                    <td>
                      <Badge bg="secondary">{item.department}</Badge>
                    </td>
                    <td>₹{item.test_price || 0}</td>
                    <td>{item.result_unit || item.unit || 'N/A'}</td>
                    <td>
                      <div className="text-truncate" style={{maxWidth: '200px'}}>
                        {item.reference_range || 'N/A'}
                      </div>
                    </td>
                    <td>
                      <Badge bg={item.excel_source ? 'success' : 'primary'}>
                        {item.excel_source ? 'Excel' : 'Manual'}
                      </Badge>
                    </td>
                    <td>
                      <Badge bg={item.is_active ? 'success' : 'danger'}>
                        {item.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </td>
                    <td>
                      <Button
                        variant="primary"
                        size="sm"
                        className="me-1"
                        onClick={() => handleEditClick(item)}
                      >
                        <FontAwesomeIcon icon={faEdit} />
                      </Button>
                      <Button
                        variant="danger"
                        size="sm"
                        onClick={() => handleDeleteClick(item)}
                      >
                        <FontAwesomeIcon icon={faTrash} />
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>

            {getFilteredData().length === 0 && (
              <div className="text-center py-4">
                <p className="text-muted">No data found matching your search criteria.</p>
              </div>
            )}
          </div>
        </Card.Body>
      </Card>

      {/* Add Modal */}
      <FormModal
        show={showAddModal}
        onHide={() => setShowAddModal(false)}
        onSubmit={handleAddSubmit}
        title="Add New Test & Result Master"
        size="xl"
      >
        {renderUnifiedForm()}
      </FormModal>

      {/* Edit Modal */}
      <FormModal
        show={showEditModal}
        onHide={() => setShowEditModal(false)}
        onSubmit={handleEditSubmit}
        title="Edit Test & Result Master"
        submitText="Save Changes"
        size="xl"
      >
        {renderUnifiedForm()}
      </FormModal>

      {/* Success Modal */}
      <SuccessModal
        show={showSuccessModal}
        onHide={() => setShowSuccessModal(false)}
        title="Success"
        message="Operation completed successfully."
      />

      {/* Error Modal */}
      <ErrorModal
        show={showErrorModal}
        onHide={() => setShowErrorModal(false)}
        title="Error"
        message={errorMessage}
      />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        show={showDeleteModal}
        onHide={() => setShowDeleteModal(false)}
        onConfirm={handleDeleteConfirm}
        title="Delete Test & Result Master"
        message={`Are you sure you want to delete "${itemToDelete?.testName}"? This will remove both test master and result master entries.`}
      />
    </div>
  );

  // Render unified form function
  function renderUnifiedForm() {
    return (
      <>
        {/* Auto-population Status */}
        {autoPopulating && (
          <Alert variant="info">
            <FontAwesomeIcon icon={faSync} className="fa-spin me-2" />
            Auto-populating fields from Excel data...
          </Alert>
        )}

        {autoPopulationStatus.notFound && (
          <Alert variant="warning">
            <FontAwesomeIcon icon={faSearch} className="me-2" />
            No Excel data found for auto-population. You can enter data manually.
          </Alert>
        )}

        {Object.keys(autoPopulationStatus).filter(key => autoPopulationStatus[key].populated).length > 0 && (
          <Alert variant="success">
            <FontAwesomeIcon icon={faDownload} className="me-2" />
            {Object.keys(autoPopulationStatus).filter(key => autoPopulationStatus[key].populated).length} fields auto-populated from Excel data.
          </Alert>
        )}

        {/* Basic Information Section */}
        <div className="border rounded p-3 mb-3">
          <h6 className="text-primary mb-3">Basic Information</h6>
          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Department*</Form.Label>
                <Form.Select
                  name="department"
                  value={formData.department}
                  onChange={handleChange}
                  required
                >
                  <option value="">Select Department</option>
                  <option value="BIOCHEMISTRY">BIOCHEMISTRY</option>
                  <option value="HAEMATOLOGY">HAEMATOLOGY</option>
                  <option value="CLINICAL_PATHOLOGY">CLINICAL PATHOLOGY</option>
                  <option value="MOLECULAR_BIOLOGY">MOLECULAR BIOLOGY</option>
                  <option value="ENDOCRINOLOGY">ENDOCRINOLOGY</option>
                  <option value="HISTOPATHOLOGY">HISTOPATHOLOGY</option>
                  <option value="SEROLOGY">SEROLOGY</option>
                  <option value="IMMUNOHAEMATOLOGY">IMMUNOHAEMATOLOGY</option>
                  <option value="MICROBIOLOGY_SURVEILLANCE">MICROBIOLOGY SURVEILLANCE</option>
                </Form.Select>
                {autoPopulationStatus.department?.populated && (
                  <Form.Text className="text-success">
                    ✅ Auto-populated from Excel data
                  </Form.Text>
                )}
              </Form.Group>
            </Col>
            <Col md={6}>
              <TextInput
                name="testName"
                label="Test Name*"
                value={formData.testName}
                onChange={handleTestNameChange}
                required
                placeholder="Enter test name or search for auto-population"
              />
              {autoPopulationStatus.testName?.populated && (
                <Form.Text className="text-success">
                  ✅ Auto-populated from Excel data
                </Form.Text>
              )}
            </Col>
          </Row>
          <Row>
            <Col md={6}>
              <TextInput
                name="test_code"
                label="Test Code*"
                value={formData.test_code}
                onChange={handleTestCodeChange}
                required
                placeholder="Enter test code for auto-population"
              />
              {autoPopulationStatus.test_code?.populated && (
                <Form.Text className="text-success">
                  ✅ Auto-populated from Excel data
                </Form.Text>
              )}
            </Col>
            <Col md={6}>
              <TextInput
                name="short_name"
                label="Short Name"
                value={formData.short_name}
                onChange={handleChange}
                placeholder="Short name or abbreviation"
              />
              {autoPopulationStatus.short_name?.populated && (
                <Form.Text className="text-success">
                  ✅ Auto-populated from Excel data
                </Form.Text>
              )}
            </Col>
          </Row>
        </div>

        {/* Test Master Section */}
        <div className="border rounded p-3 mb-3">
          <h6 className="text-primary mb-3">
            <FontAwesomeIcon icon={faFlask} className="me-2" />
            Test Master Information
          </h6>
          <Row>
            <Col md={6}>
              <TextInput
                name="method"
                label="Method"
                value={formData.method}
                onChange={handleChange}
                placeholder="Test method"
              />
              {autoPopulationStatus.method?.populated && (
                <Form.Text className="text-success">
                  ✅ Auto-populated from Excel data
                </Form.Text>
              )}
            </Col>
            <Col md={6}>
              <NumberInput
                name="method_code"
                label="Method Code"
                value={formData.method_code}
                onChange={handleChange}
                placeholder="Method code"
              />
              {autoPopulationStatus.method_code?.populated && (
                <Form.Text className="text-success">
                  ✅ Auto-populated from Excel data
                </Form.Text>
              )}
            </Col>
          </Row>
          <Row>
            <Col md={6}>
              <TextInput
                name="container"
                label="Container"
                value={formData.container}
                onChange={handleChange}
                placeholder="Container type"
              />
              {autoPopulationStatus.container?.populated && (
                <Form.Text className="text-success">
                  ✅ Auto-populated from Excel data
                </Form.Text>
              )}
            </Col>
            <Col md={6}>
              <NumberInput
                name="test_price"
                label="Test Price"
                value={formData.test_price}
                onChange={handleChange}
                min={0}
                step={0.01}
                placeholder="0.00"
              />
              {autoPopulationStatus.test_price?.populated && (
                <Form.Text className="text-success">
                  ✅ Auto-populated from Excel data
                </Form.Text>
              )}
            </Col>
          </Row>
          <Row>
            <Col md={12}>
              <Form.Group className="mb-3">
                <Form.Label>Instructions</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={2}
                  name="instructions"
                  value={formData.instructions}
                  onChange={handleChange}
                  placeholder="Special instructions for test collection or processing"
                />
                {autoPopulationStatus.instructions?.populated && (
                  <Form.Text className="text-success">
                    ✅ Auto-populated from Excel data
                  </Form.Text>
                )}
              </Form.Group>
            </Col>
          </Row>
        </div>

        {/* Result Master Section */}
        <div className="border rounded p-3 mb-3">
          <h6 className="text-primary mb-3">
            <FontAwesomeIcon icon={faVial} className="me-2" />
            Result Master Information
          </h6>
          <Row>
            <Col md={6}>
              <TextInput
                name="unit"
                label="Result Unit"
                value={formData.unit}
                onChange={handleChange}
                placeholder="mg/dl, mmol/L, etc."
              />
              {autoPopulationStatus.unit?.populated && (
                <Form.Text className="text-success">
                  ✅ Auto-populated from Excel data
                </Form.Text>
              )}
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Result Type</Form.Label>
                <Form.Select
                  name="result_type"
                  value={formData.result_type}
                  onChange={handleChange}
                >
                  <option value="Numeric">Numeric</option>
                  <option value="Pick List">Pick List</option>
                  <option value="Template">Template</option>
                  <option value="Text">Text</option>
                </Form.Select>
                {autoPopulationStatus.result_type?.populated && (
                  <Form.Text className="text-success">
                    ✅ Auto-populated from Excel data
                  </Form.Text>
                )}
              </Form.Group>
            </Col>
          </Row>
          <Row>
            <Col md={12}>
              <Form.Group className="mb-3">
                <Form.Label>Reference Range</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={2}
                  name="reference_range"
                  value={formData.reference_range}
                  onChange={handleChange}
                  placeholder="Normal reference range for the test"
                />
                {autoPopulationStatus.reference_range?.populated && (
                  <Form.Text className="text-success">
                    ✅ Auto-populated from Excel data
                  </Form.Text>
                )}
              </Form.Group>
            </Col>
          </Row>
          <Row>
            <Col md={4}>
              <NumberInput
                name="critical_low"
                label="Critical Low"
                value={formData.critical_low}
                onChange={handleChange}
                placeholder="Critical low value"
              />
              {autoPopulationStatus.critical_low?.populated && (
                <Form.Text className="text-success">
                  ✅ Auto-populated from Excel data
                </Form.Text>
              )}
            </Col>
            <Col md={4}>
              <NumberInput
                name="critical_high"
                label="Critical High"
                value={formData.critical_high}
                onChange={handleChange}
                placeholder="Critical high value"
              />
              {autoPopulationStatus.critical_high?.populated && (
                <Form.Text className="text-success">
                  ✅ Auto-populated from Excel data
                </Form.Text>
              )}
            </Col>
            <Col md={4}>
              <NumberInput
                name="decimal_places"
                label="Decimal Places"
                value={formData.decimal_places}
                onChange={handleChange}
                min={0}
                max={5}
                placeholder="0"
              />
              {autoPopulationStatus.decimal_places?.populated && (
                <Form.Text className="text-success">
                  ✅ Auto-populated from Excel data
                </Form.Text>
              )}
            </Col>
          </Row>
        </div>

        {/* Additional Settings */}
        <div className="border rounded p-3 mb-3">
          <h6 className="text-primary mb-3">Additional Settings</h6>
          <Row>
            <Col md={6}>
              <NumberInput
                name="reporting_days"
                label="Reporting Days"
                value={formData.reporting_days}
                onChange={handleChange}
                min={0}
                placeholder="0"
              />
              {autoPopulationStatus.reporting_days?.populated && (
                <Form.Text className="text-success">
                  ✅ Auto-populated from Excel data
                </Form.Text>
              )}
            </Col>
            <Col md={6}>
              <TextInput
                name="min_sample_qty"
                label="Minimum Sample Quantity"
                value={formData.min_sample_qty}
                onChange={handleChange}
                placeholder="e.g., 5ml, 2ml"
              />
              {autoPopulationStatus.min_sample_qty?.populated && (
                <Form.Text className="text-success">
                  ✅ Auto-populated from Excel data
                </Form.Text>
              )}
            </Col>
          </Row>
          <Row>
            <Col md={12}>
              <Form.Check
                type="switch"
                id="is_active"
                name="is_active"
                label="Active"
                checked={formData.is_active}
                onChange={handleChange}
              />
            </Col>
          </Row>
        </div>
      </>
    );
  }
};

export default UnifiedTestResultMaster;