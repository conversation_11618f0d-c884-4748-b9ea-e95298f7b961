[{"id": 1, "invoice_number": "INV-********-0001", "routing_id": 16, "sample_id": 58, "from_tenant_id": 1, "to_tenant_id": 2, "created_by": 19, "created_at": "2025-06-15T09:55:34.434069", "updated_at": "2025-06-15T09:59:51.547068", "invoice_date": "2025-06-15", "due_date": "2025-07-15", "status": "sent", "subtotal": 78.99, "tax_rate": 0.18, "tax_amount": 14.22, "total_amount": 93.21, "currency": "INR", "notes": "", "line_items": [{"description": "dfdf", "quantity": 1, "unit_price": "33.99", "total": 33.99}, {"description": "g", "quantity": 1, "unit_price": "45", "total": 45.0}]}, {"id": 2, "invoice_number": "INV-********-0002", "routing_id": 13, "sample_id": 74, "from_tenant_id": 1, "to_tenant_id": 2, "created_by": 2, "created_at": "2025-06-15T09:30:00", "updated_at": "2025-06-15T09:30:00", "invoice_date": "2025-06-15", "due_date": "2025-07-15", "status": "sent", "subtotal": 1500.0, "tax_rate": 0.18, "tax_amount": 270.0, "total_amount": 1770.0, "currency": "INR", "notes": "Sample processing and analysis charges", "line_items": [{"description": "Sample Processing Fee", "quantity": 1, "unit_price": 800.0, "total": 800.0}, {"description": "Laboratory Analysis", "quantity": 1, "unit_price": 500.0, "total": 500.0}, {"description": "Report Generation", "quantity": 1, "unit_price": 200.0, "total": 200.0}]}, {"id": 3, "invoice_number": "INV-********-0003", "routing_id": 14, "sample_id": 63, "from_tenant_id": 1, "to_tenant_id": 2, "created_by": 2, "created_at": "2025-06-15T10:00:00", "updated_at": "2025-06-15T10:15:00", "invoice_date": "2025-06-15", "due_date": "2025-07-15", "status": "paid", "subtotal": 2000.0, "tax_rate": 0.18, "tax_amount": 360.0, "total_amount": 2360.0, "currency": "INR", "notes": "Comprehensive testing package - Payment received", "line_items": [{"description": "Advanced Testing Package", "quantity": 1, "unit_price": 1200.0, "total": 1200.0}, {"description": "Express Processing", "quantity": 1, "unit_price": 500.0, "total": 500.0}, {"description": "Digital Report Delivery", "quantity": 1, "unit_price": 300.0, "total": 300.0}]}, {"id": 4, "invoice_number": "INV-********-0004", "routing_id": 17, "sample_id": 45, "from_tenant_id": 1, "to_tenant_id": 2, "created_by": 19, "created_at": "2025-06-15T10:16:10.667631", "updated_at": "2025-06-15T11:32:14.946805", "invoice_date": "2025-06-15", "due_date": "2025-07-15", "status": "draft", "subtotal": 800.0, "tax_rate": 0.18, "tax_amount": 144.0, "total_amount": 944.0, "currency": "INR", "notes": "Automatically generated invoice for sample routing\n\nOwnership transferred to destination facility on 2025-06-15", "line_items": [{"description": "Sample Processing Fee", "quantity": 1, "unit_price": 500.0, "total": 500.0}, {"description": "Laboratory Analysis", "quantity": 1, "unit_price": 300.0, "total": 300.0}], "ownership_transferred": true, "original_owner": 1, "ownership_transferred_at": "2025-06-15T11:32:14.946805", "ownership_transferred_by": 2}, {"id": 5, "invoice_number": "INV-********-0001", "routing_id": 18, "sample_id": 52, "from_tenant_id": 1, "to_tenant_id": 2, "created_by": 4, "created_at": "2025-06-25T17:19:27.684017", "updated_at": "2025-06-25T17:21:28.353177", "invoice_date": "2025-06-25", "due_date": "2025-07-25", "status": "draft", "subtotal": 800.0, "tax_rate": 0.18, "tax_amount": 144.0, "total_amount": 944.0, "currency": "INR", "notes": "Automatically generated invoice for sample routing\n\nOwnership transferred to destination facility on 2025-06-25", "line_items": [{"description": "Sample Processing Fee", "quantity": 1, "unit_price": 500.0, "total": 500.0}, {"description": "Laboratory Analysis", "quantity": 1, "unit_price": 300.0, "total": 300.0}], "ownership_transferred": true, "original_owner": 1, "ownership_transferred_at": "2025-06-25T17:21:28.353177", "ownership_transferred_by": 5}]